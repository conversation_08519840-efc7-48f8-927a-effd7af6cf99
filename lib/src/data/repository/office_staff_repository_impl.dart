import 'package:dio/dio.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';

import '../../core/config/app_strings.dart';
import '../../core/network/api_config.dart';
import '../../core/network/dio_client.dart';
import '../../core/services/api_error_handler.dart';
import '../../core/services/exceptions.dart';
import '../../domain/models/role.dart';
import '../../domain/repository/office_staff_repository.dart';

class OfficeStaffRepositoryImpl extends OfficeStaffRepository {
  OfficeStaffRepositoryImpl();

  static const String officeStaffRegisterUrl = APIConfig.brokerageSubUser;
  static const String roleListUrl = APIConfig.brokerageRole;
  static const String statusUrl = APIConfig.officeStaffStatus;

  @override
  Future<dynamic> registerOfficeStaff(Map<String, dynamic> requestBody) async {
    try {
      final dio = await DioClient.getDio();
      final response = await dio.post(
        officeStaffRegisterUrl,
        data: requestBody,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        return response.data as Map<String, dynamic>;
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(
        e,
        'Failed to register office staff',
      );
    } catch (e) {
      throw ApiException(
        message: 'Unexpected error: ${e.toString()}',
        statusCode: 500,
      );
    }
  }

  @override
  Future<bool> uploadOfficeStaffFile(Map<String, dynamic> requestBody) async {
    try {
      final dio = await DioClient.getDioForMultipart();

      // Create FormData for multipart upload
      FormData formData = FormData();

      // Add non-file fields
      requestBody.forEach((key, value) {
        if (key != 'file') {
          formData.fields.add(MapEntry(key, value.toString()));
        }
      });

      // Add file if present
      if (requestBody['file'] != null) {
        final file = requestBody['file'];
        if (file is PlatformFile) {
          try {
            MultipartFile multipartFile = await _createMultipartFile(file);

            formData.files.add(MapEntry('file', multipartFile));
          } catch (e) {
            throw ApiException(
              message: 'Failed to process file: ${file.name}',
              statusCode: 400,
            );
          }
        } else {
          throw ApiException(
            message: 'Invalid file type provided',
            statusCode: 400,
          );
        }
      } else {
        throw ApiException(
          message: 'No file provided for upload',
          statusCode: 400,
        );
      }

      final response = await dio.post(APIConfig.uploadFile, data: formData);

      if (response.statusCode == 200 || response.statusCode == 201) {
        return true;
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(
        e,
        'Failed to upload office staff file',
      );
    } catch (e) {
      throw ApiException(
        message: '$unexpectedError: ${e.toString()}',
        statusCode: 500,
      );
    }
  }

  Future<MultipartFile> _createMultipartFile(PlatformFile file) async {
    if (kIsWeb) {
      // For web platform, use bytes
      if (file.bytes != null) {
        return MultipartFile.fromBytes(file.bytes!, filename: file.name);
      } else {
        throw ApiException(
          message: 'File bytes not available for web upload: ${file.name}',
          statusCode: 400,
        );
      }
    } else {
      // For mobile/desktop platforms, use file path
      if (file.path != null) {
        return await MultipartFile.fromFile(file.path!, filename: file.name);
      } else {
        throw ApiException(
          message: 'File path not available for mobile upload: ${file.name}',
          statusCode: 400,
        );
      }
    }
  }

  @override
  Future<List<Role>> getRoleListNames() async {
    try {
      final dio = await DioClient.getDio();
      final response = await dio.get(roleListUrl);
      if (response.statusCode == 200) {
        final List<dynamic> filterOptionsJson = response.data ?? [];
        final filterOptions = filterOptionsJson
            .map((e) => Role.fromJson(e))
            .where(
              (role) => ![
                'Platform Owner',
                'Platform Admin',
                'Brokerage',
                'Agent',
              ].contains(role.value),
            )
            .toList();
        return filterOptions;
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(e, errorFetchingFilterOptions);
    } catch (e) {
      throw ApiException(message: e.toString(), statusCode: 500);
    }
  }

   @override
  Future<dynamic> getOfficeStaffStatus(Map<String, dynamic> requestBody) async {
    try {
      final dio = await DioClient.getDio();
      final response = await dio.post(
        statusUrl,
        data: requestBody,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        return response.data as Map<String, dynamic>;
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(
        e,
        'Failed to get office staff status',
      );
    } catch (e) {
      throw ApiException(
        message: 'Unexpected error: ${e.toString()}',
        statusCode: 500,
      );
    }
  }
}
