import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

class AppTheme {
  const AppTheme._();

  static const Color black = Color(0xFF000000);
  static const Color white = Color(0xFFFFFFFF);
  static const Color primaryColor = Color(0xFF2697FF);
  static const Color primaryBlueColor = Color(0xFF1A71DB);
  static const Color secondaryColor = Color(0xFF2A2D3E);
  static const Color scaffoldBgColor = Color(0xFFCCE0F4);
  static const Color blueCardColor = Color(0xFF0047AB);

  static const Color primaryTextColor = Color(0xFF333333);
  static const Color secondaryTextColor = Color(0xFF7C7E89);
  static const Color ternaryTextColor = Color(0xFF5E5E5E);

  static const Color greyRoundBg = Color(0xFFECECEC);

  static const Color orContinueWithColor = Color(0xFF606060);
  static const Color textFieldBorder = Color(0xFFCCCCCC);
  static const Color textFieldHint = Color(0xFF777777);
  static const Color checkboxColor = Color(0xFF8E8E93);

  // Header
  static const Color headerIconBgColor = Color(0xFFE4EBF3);

  static const Color borderColor = Color(0xFFECEBEF);
  static const Color loginBgColor = Color(0xFF1684FF);

  static const Color commissionCardColor = Color(0xFF00409D);
  static const Color commissionDropDownBgColor = Color(0xFF0259D8);
  static const Color commissionCardDarkColor = Color(0xFF002F7A);
  static const Color commissionSalesTextColor = Color(0xFF90B5EA);

  // Table
  static const Color paginationActiveBg = Color(0xFF28569F);
  static const Color paginationInactiveBg = Color(0xFFF5F5F5);
  static const Color searchbarBg = Color(0xFFE4EBF3);
  static const Color pageSummaryLabelColor = Color(0xFFB5B7C0);
  static const Color tableColumnHeaderColor = Color(0xFF7C7E89);
  static const Color viewMoreBlue = Color(0xFF4DA1FF);

  //icon
  static const Color roundIconBgColor = Color(0xFFEEEEEE);
  static const Color roundIconColor = Color(0xFF1A71DB);

  static const Color viewMoreBgColor = Color(0xFFE4E4E4);
  static const Color hierarchyLineColor = Color(0xFF666666);
  static const Color textFieldMandatoryColor = Color(0xFFFF0000);
  static const Color docUploadBgColor = Color(0xFFFAFAFA);

  static const Color breadcrumbParentTextColor = Color(0xFF374151);
  static const Color breadcrumbChildTextColor = Color(0xFF737373);
  static const Color breadcrumbBgColor = Color(0xFFF8FAFE);
  static const Color breadcrumbContainerBgColor = Color(0xFFB7D1EB);
  static const Color breadcrumbArrowColor = Color(0xFF666666);

  static const Color cancelBgColor = Color(0xFFCCE0F4);
  static const Color formSubsectionBgColor = Color(0xFFF6F6F6);

  static const Color signatureUploadBorderColor = Color(0xFFCDCDCD);
  static const Color signatureUploadDottedColor = Color(0xFFAAAAAA);
  static const Color salesLegendBorderColor = Color(0xFFDFDFDF);

  static const Color filterBgColor = Color(0xFFE8F3FF);
  static const Color filterResultBannerColor = Color(0xFFFFF6C5);
  static const Color filterResultBannerBorderColor = Color(0xFFE8D985);

  static const Color statusActiveBg = Color(0x6116C098);
  static const Color statusActiveText = Color(0xFF008767);
  static const Color statusInactiveBg = Color(0xFFFFC5C5);
  static const Color statusInactiveText = Color(0xFFDF0404);
  static const Color viewAction = Color(0xFF1A71DB);
  static const Color applyFilterTooltip = Color(0xFFFFF6C5);
  static const Color comboBoxBorder = Color(0xFFCCCCCC);
  static const Color selectedComboBoxBorder = Color(0xFF1A71DB);
  static const Color tableHeaderFont = Color(0xFF7C7E89);
  static const Color tableDataFont = Color(0xFF333333);

  static const Color agentStatusActiveBg = Color(0xFF16C098);
  static const Color agentStatusInactiveBg = Color(0xFFFFC5C5);
  static const Color agentStatusActiveText = Color(0xFF2E7D32);
  static const Color agentStatusInactiveText = Color(0xFFD32F2F);
  static const List<Color> salesColors = [
    Color(0xFF8B1538),
    Color(0xFFFF9500),
    Color(0xFF4A7C59),
    Color(0xFF007AFF),
    Color(0xFF5AC8FA),
    Color(0xFFFFCC02),
    Color(0xFF1D1D1F),
    Color(0xFFE91E63),
    Color(0xFF9013FE),
    Color(0xFF536DFE),
    Color(0xFF00C853),
    Color(0xFFAA00FF),
  ];
  static const Color primaryBlue = Color(0xFF4A90E2);
  static const Color primaryBlueGradientStart = Color(0xFF4A90E2);
  static const Color primaryBlueGradientEnd = Color(0xFF357ABD);

  // Background Colors
  static const Color backgroundColor = Color(0xFFF5F5F5);
  static const Color cardBackground = Colors.white;
  static const Color uploadAreaBackground = Color(0xFFF8F9FA);
  static const Color transparent = Colors.transparent;

  // Text Colors
  static const Color primaryText = Color(0xFF2C3E50);
  static const Color secondaryText = Color(0xFF7F8C8D);
  static const Color whiteText = Colors.white;

  // Border Colors
  static const Color dashedBorder = Color(0xFFBDC3C7);
  static const Color progressBarBackground = Color(0xFFE8F4FD);

  // Button Colors
  static const Color clearButtonColor = Color(0xFFB3D4F1);
  static const Color clearButtonTextColor = Color(0xFF2C3E50);

  // File Upload Colors
  static const Color pdfIconColor = Color(0xFFE74C3C);
  static const Color uploadIconColor = Color(0xFF4A90E2);

  // Status Colors
  static const Color successColor = Color(0xFF27AE60);
  static const Color errorColor = Color(0xFFE74C3C);
  static const Color warningColor = Color(0xFFF39C12);
  static ThemeData get lightTheme {
    return ThemeData(
      primarySwatch: Colors.blue,
      primaryColor: primaryBlue,
      scaffoldBackgroundColor: backgroundColor,
      fontFamily: 'Roboto',

      // AppBar Theme
      appBarTheme: const AppBarTheme(
        backgroundColor: primaryBlue,
        foregroundColor: whiteText,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: whiteText,
        ),
      ),

      // Card Theme
      cardTheme: CardThemeData(
        color: cardBackground,
        elevation: 8,
        shadowColor: Colors.black.withOpacity(0.1),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      ),

      // Elevated Button Theme
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryBlue,
          foregroundColor: whiteText,
          elevation: 2,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          textStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
      ),

      // Outlined Button Theme
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: clearButtonTextColor,
          side: const BorderSide(color: clearButtonColor),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          textStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
        ),
      ),

      // Text Theme
      textTheme: const TextTheme(
        headlineLarge: TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.bold,
          color: whiteText,
        ),
        headlineMedium: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: primaryText,
        ),
        bodyLarge: TextStyle(fontSize: 16, color: primaryText),
        bodyMedium: TextStyle(fontSize: 14, color: secondaryText),
        bodySmall: TextStyle(fontSize: 12, color: secondaryText),
      ),
    );
  }
}
