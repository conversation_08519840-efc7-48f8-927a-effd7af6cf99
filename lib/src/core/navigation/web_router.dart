import 'package:go_router/go_router.dart';
import '../../presentation/screens/auth/password_recovery_screen.dart';
import '../../presentation/screens/auth/email_verification_screen.dart';
import '../../presentation/screens/office_staff/register_office_staff_screen.dart';
import '../../presentation/screens/auth/create_password_screen.dart';
import '/src/presentation/screens/layout/signup_layout.dart';
import '../../presentation/screens/agent/agent_registration_screen.dart';
import '../../presentation/screens/broker/register_broker_screen.dart';
import '/main_layout_screen.dart';
import '../../presentation/screens/auth/auth_wrapper.dart';

class RouteSegments {
  static const login = '';
  static const mainLayout = 'home';
  static const dashboard = 'dashboard';
  static const brokerages = 'brokerages';
  static const agents = 'agents';
  static const sales = 'sales';
  static const reports = 'reports';
  static const registerBroker = 'register-brokerage';
  static const registerAgent = 'register-agent';
  static const registerStaff = 'register-office-staff';
  static const agentNetwork = 'agent-network';
  static const saleReviewDoc = 'sale-review-doc';
  static const signupBroker = 'signup-brokerage';
  static const signupAgent = 'signup-agent';
  static const createPassword = 'create-password';
  static const uploadDocument = 'upload-closing-document';
  static const emailVerification = 'email-verification';
  static const forgotPassword = 'forgot-password';
  static const resetPassword = 'reset-password';
}

enum AppRoutes {
  login('/'),
  mainLayout('/${RouteSegments.mainLayout}'),
  dashboard('/${RouteSegments.mainLayout}/${RouteSegments.dashboard}'),
  brokerages('/${RouteSegments.mainLayout}/${RouteSegments.brokerages}'),
  agents('/${RouteSegments.mainLayout}/${RouteSegments.agents}'),
  sales('/${RouteSegments.mainLayout}/${RouteSegments.sales}'),
  reports('/${RouteSegments.mainLayout}/${RouteSegments.reports}'),
  registerBroker(
    '/${RouteSegments.mainLayout}/${RouteSegments.registerBroker}',
  ),
  registerAgent('/${RouteSegments.mainLayout}/${RouteSegments.registerAgent}'),
  registerOfficeStaff(
    '/${RouteSegments.mainLayout}/${RouteSegments.registerStaff}',
  ),
  agentNetwork('/${RouteSegments.mainLayout}/${RouteSegments.agentNetwork}'),
  saleReviewDoc('/${RouteSegments.mainLayout}/${RouteSegments.saleReviewDoc}'),

  signupBroker('/${RouteSegments.signupBroker}'),
  signupAgent('/${RouteSegments.signupAgent}'),
  createPassword('/${RouteSegments.createPassword}'),
  uploadDocument(
    '/${RouteSegments.mainLayout}/${RouteSegments.uploadDocument}',
  ),
  emailVerification('/${RouteSegments.emailVerification}'),
  forgotPassword('/${RouteSegments.forgotPassword}'),
  resetPassword('/${RouteSegments.resetPassword}');

  const AppRoutes(this.path);
  final String path;
}

final GoRouter appRouter = GoRouter(
  initialLocation: AppRoutes.login.path,
  routes: [
    GoRoute(
      path: AppRoutes.login.path,
      builder: (context, state) => const AuthWrapper(),
    ),
    GoRoute(
      path: '/home/<USER>',
      builder: (context, state) {
        final tab = state.pathParameters['tab'] ?? 'dashboard';
        return MainLayoutScreen(initialTab: tab);
      },
    ),
    GoRoute(
      path: AppRoutes.mainLayout.path,
      redirect: (context, state) => AppRoutes.dashboard.path,
    ),
    GoRoute(
      path: AppRoutes.signupBroker.path,
      builder: (context, state) {
        final token = state.uri.queryParameters['invite-id'];
        return SignupLayoutScreen(child: RegisterBrokerScreen());
      },
    ),
    GoRoute(
      path: AppRoutes.signupAgent.path,
      builder: (context, state) {
        final token = state.uri.queryParameters['invite-id'];

        final extras = state.extra as Map<String, dynamic>?;
        final idToken = extras?["idToken"];
        final signinType = extras?['signinType'] ?? SigninType.none;

        return SignupLayoutScreen(
          child: AgentRegistrationScreen(
            inviteId: token,
            isSignUp: true,
            idToken: idToken,
            signinType: signinType,
          ),
        );
      },
    ),
    GoRoute(
      path: AppRoutes.signupAgent.path,
      builder: (context, state) {
        final token = state.uri.queryParameters['invite-id'];
        return SignupLayoutScreen(child: RegisterOfficeStaffScreen());
      },
    ),
    GoRoute(
      path: AppRoutes.createPassword.path,
      builder: (context, state) {
        final token = state.uri.queryParameters['invite-id'];
        String? userId = state.uri.queryParameters['user-id'];
        String? email = state.uri.queryParameters['email'];

        final extra = state.extra as Map<String, dynamic>?;

        // when pass values from signup
        email ??= extra?['email'] as String?;
        userId ??= extra?['userId'] as String?;

        return SignupLayoutScreen(
          child: CreatePasswordScreen(
            inviteId: token,
            email: email,
            userId: userId,
          ),
        );
      },
    ),
    GoRoute(
      path: AppRoutes.emailVerification.path,
      builder: (context, state) =>
          SignupLayoutScreen(child: EmailVerificationScreen()),
    ),
    GoRoute(
      path: AppRoutes.forgotPassword.path,
      builder: (context, state) {
        String? token = state.uri.queryParameters['token'];

        final extra = state.extra as Map<String, dynamic>?;

        final recoveryType =
            extra?['type'] as PasswordRecoveryType? ??
            PasswordRecoveryType.forgot;

        return recoveryType == PasswordRecoveryType.forgot
            ? SignupLayoutScreen(
                child: PasswordRecoveryScreen(
                  recoveryType: recoveryType,
                  token: token,
                ),
              )
            : PasswordRecoveryScreen(recoveryType: recoveryType, token: '');
      },
    ),
    GoRoute(
      path: AppRoutes.resetPassword.path,
      builder: (context, state) {
        final extra = state.extra as Map<String, dynamic>?;

        final recoveryType =
            extra?['type'] as PasswordRecoveryType? ??
            PasswordRecoveryType.reset;

        return SignupLayoutScreen(
          child: PasswordRecoveryScreen(recoveryType: recoveryType, token: ''),
        );
      },
    ),
  ],
);
