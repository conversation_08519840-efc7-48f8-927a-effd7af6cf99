import 'dart:async';

class ApiException implements Exception {
  final String message;
  final int? statusCode;

  ApiException({required this.message, this.statusCode});

  @override
  String toString() => message;
}

class InvalidCredentialsException implements Exception {
  final String message;
  final int? statusCode;

  InvalidCredentialsException({required this.message, this.statusCode});

  @override
  String toString() => message;
}

class UnauthorizedException implements Exception {
  final String message;
  final int? statusCode;

  UnauthorizedException({required this.message, this.statusCode});

  @override
  String toString() => message;
}

/// Utility class for handling different types of exceptions consistently
class ExceptionHandler {
  /// Get user-friendly error message from any exception
  static String getErrorMessage(dynamic exception) {
    if (exception is ApiException) {
      return exception.message;
    }

    if (exception is InvalidCredentialsException) {
      return exception.message;
    }

    if (exception is UnauthorizedException) {
      return exception.message;
    }

    // Handle other common exception types
    if (exception is FormatException) {
      return 'Invalid data format: ${exception.message}';
    }

    if (exception is TimeoutException) {
      return 'Request timed out. Please check your connection and try again.';
    }

    // Default fallback
    return exception.toString();
  }

  /// Get status code from exception if available
  static int? getStatusCode(dynamic exception) {
    if (exception is ApiException) {
      return exception.statusCode;
    }

    if (exception is InvalidCredentialsException) {
      return exception.statusCode;
    }

    if (exception is UnauthorizedException) {
      return exception.statusCode;
    }

    return null;
  }
}
