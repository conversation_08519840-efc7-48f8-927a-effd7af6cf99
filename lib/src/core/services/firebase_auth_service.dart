import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '/src/presentation/shared/components/customDialogues/alert_dialogue.dart';
import 'package:universal_html/html.dart' as html;
import 'package:dio/dio.dart';
import 'dart:async';

import '../config/app_strings.dart';

class FirebaseAuthService {
  static final FirebaseAuthService _instance = FirebaseAuthService._internal();
  factory FirebaseAuthService() => _instance;
  FirebaseAuthService._internal();

  final FirebaseAuth _firebaseAuth = FirebaseAuth.instance;

  /// Sign in with Google using Firebase Auth directly with faster error detection
  Future<GoogleSignInResult> signInWithGoogle() async {
    try {
      // Create a new provider
      GoogleAuthProvider googleProvider = GoogleAuthProvider();

      googleProvider.addScope(
        'https://www.googleapis.com/auth/contacts.readonly',
      );
      googleProvider.addScope('email');
      googleProvider.addScope('profile');

      // Force account selection dialog every time
      googleProvider.setCustomParameters({'prompt': 'select_account'});
      await _firebaseAuth.signOut();

      // Sign in with popup for web - let Firebase handle its own timing
      final UserCredential userCredential = await _firebaseAuth.signInWithPopup(
        googleProvider,
      );
      // Check if we actually got a user
      if (userCredential.user == null) {
        return GoogleSignInResult(
          success: false,
          error: 'Sign-in was cancelled or failed to complete.',
        );
      }

      // Get the ID token for backend verification
      final String? idToken = await userCredential.user?.getIdToken();

      return GoogleSignInResult(
        success: true,
        user: userCredential.user,
        idToken: idToken,
        accessToken: null, // Not available with this method
        email: userCredential.user?.email,
        displayName: userCredential.user?.displayName,
        photoUrl: userCredential.user?.photoURL,
      );
    } catch (e) {
      // Handle different types of exceptions
      String errorMessage = AuthExceptonHelper().handleAuthException(e);

      return GoogleSignInResult(success: false, error: errorMessage);
    }
  }

  /// Sign in with Apple using Firebase Auth with email masking warning
  Future<GoogleSignInResult> signInWithApple({BuildContext? context}) async {
    try {
      // Show warning dialog if context is provided (for UI flows)
      if (context != null) {
        final shouldProceed = await _showAppleSignInWarningDialog(context);
        if (!shouldProceed) {
          return GoogleSignInResult(
            success: false,
            error: 'Sign-in was cancelled by user.',
            showWarning: false,
          );
        }
      }

      // Create Apple provider for Firebase Auth
      final appleProvider = OAuthProvider("apple.com");
      appleProvider.addScope('email');
      appleProvider.addScope('name');

      // Set custom parameters for Apple Sign-In
      appleProvider.setCustomParameters({'locale': 'en'});

      // Sign in with popup for web - let Firebase handle its own timing
      final UserCredential userCredential = await _firebaseAuth.signInWithPopup(
        appleProvider,
      );

      // Check if we actually got a user
      if (userCredential.user == null) {
        debugPrint('Apple sign-in completed but no user returned');
        return GoogleSignInResult(
          success: false,
          error: 'Sign-in was cancelled or failed to complete.',
        );
      }

      // Get the ID token for backend verification
      final String? idToken = await userCredential.user?.getIdToken();

      return GoogleSignInResult(
        success: true,
        user: userCredential.user,
        idToken: idToken,
        accessToken: null, // Not available with this method
        email: userCredential.user?.email,
        displayName: userCredential.user?.displayName,
        photoUrl: userCredential.user?.photoURL,
      );
    } catch (e) {
      // Handle different types of exceptions
      String errorMessage = AuthExceptonHelper().handleAuthException(e);

      return GoogleSignInResult(success: false, error: errorMessage);
    }
  }

  /// Shows warning dialog for Apple sign-in email masking
  Future<bool> _showAppleSignInWarningDialog(BuildContext context) async {
    if (!context.mounted) return false;

    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false, // Prevent dismissing by tapping outside
      builder: (BuildContext dialogContext) {
        return showAlertDialogue(
          context,
          width: 800,
          titlePadding: 1.5,
          title: appleEmailWarningTitle,
          content: appleEmailWarningContent,
          positiveButtonText: proceed,
          negativeButtonText: cancel,
        );
      },
    );

    return result ?? false; // Return false if dialog was dismissed
  }

  /// Sign out from Firebase
  Future<void> signOut() async {
    try {
      await _firebaseAuth.signOut();

      // Clear browser storage on web to ensure clean state
      if (kIsWeb) {
        try {
          html.window.localStorage.clear();
          html.window.sessionStorage.clear();
        } catch (e) {}
      }
    } catch (e) {
      debugPrint('Error signing out: $e');
      rethrow;
    }
  }

  /// Get current Firebase user
  User? get currentUser => _firebaseAuth.currentUser;

  /// Check if user is signed in
  bool get isSignedIn => _firebaseAuth.currentUser != null;

  /// Get current user's ID token
  Future<String?> getCurrentUserIdToken() async {
    try {
      return await _firebaseAuth.currentUser?.getIdToken();
    } catch (e) {
      debugPrint('Error getting ID token: $e');
      return null;
    }
  }

  /// Listen to auth state changes
  Stream<User?> get authStateChanges => _firebaseAuth.authStateChanges();
}

/// Result class for Google Sign-In operations
class GoogleSignInResult {
  final bool success;
  final String? error;
  final User? user;
  final String? idToken;
  final String? accessToken;
  final String? email;
  final String? displayName;
  final String? photoUrl;
  final bool showWarning;

  GoogleSignInResult({
    required this.success,
    this.error,
    this.user,
    this.idToken,
    this.accessToken,
    this.email,
    this.displayName,
    this.photoUrl,
    this.showWarning = true,
  });

  @override
  String toString() {
    return 'GoogleSignInResult(success: $success, error: $error, email: $email)';
  }
}

class AuthExceptonHelper {
  /// Handle different types of authentication exceptions by status code
  String handleAuthException(dynamic exception) {
    // Handle Firebase Auth exceptions
    if (exception is FirebaseAuthException) {
      switch (exception.code) {
        case 'cancelled-popup-request':
          return 'Sign-in was cancelled. Please try again.';
        case 'popup-blocked':
          return 'Pop-up was blocked by the browser. Please allow pop-ups and try again.';
        case 'popup-closed-by-user':
          return 'Sign-in was cancelled by user.';
        case 'unauthorized-domain':
          return 'This domain is not authorized for OAuth operations.';
        case 'operation-not-allowed':
          return 'This sign-in method is not enabled.';
        case 'invalid-credential':
          return 'The provided credential is invalid.';
        case 'user-disabled':
          return 'This user account has been disabled.';
        case 'user-not-found':
          return 'No user found with this credential.';
        case 'wrong-password':
          return 'Incorrect password.';
        case 'too-many-requests':
          return 'Too many failed attempts. Please try again later.';
        case 'network-request-failed':
          return 'Network error. Please check your connection and try again.';
        default:
          return 'Authentication failed: ${exception.message ?? exception.code}';
      }
    }

    // Handle Dio exceptions (HTTP status codes)
    if (exception is DioException) {
      final statusCode = exception.response?.statusCode;
      final message = exception.response?.data?['message'] ?? exception.message;

      switch (statusCode) {
        case 400:
          return 'Bad request: ${message ?? 'Invalid request parameters'}';
        case 401:
          return 'Unauthorized: ${message ?? 'Authentication failed'}';
        case 403:
          return 'Forbidden: ${message ?? 'Access denied'}';
        case 404:
          return 'Not found: ${message ?? 'Service not available'}';
        case 408:
          return 'Request timeout: ${message ?? 'Request took too long'}';
        case 409:
          return 'Conflict: ${message ?? 'Resource conflict'}';
        case 422:
          return 'Validation failed: ${message ?? 'Invalid data provided'}';
        case 429:
          return 'Too many requests: ${message ?? 'Rate limit exceeded'}';
        case 500:
          return 'Server error: ${message ?? 'Internal server error'}';
        case 502:
          return 'Bad gateway: ${message ?? 'Server is temporarily unavailable'}';
        case 503:
          return 'Service unavailable: ${message ?? 'Service is temporarily down'}';
        case 504:
          return 'Gateway timeout: ${message ?? 'Server response timeout'}';
        default:
          return 'Network error (${statusCode ?? 'Unknown'}): ${message ?? exception.toString()}';
      }
    }

    // Handle other exceptions
    return 'An unexpected error occurred: ${exception.toString()}';
  }
}
