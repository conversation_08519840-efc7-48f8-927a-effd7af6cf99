// App General
const String appName = "NeoRevv";
const String appDescription =
    "A powerful tool to manage agents, track commissions, and streamline real estate operations.";
const String appDescriptionP1 = 'A ';
const String appDescriptionP2 = 'powerful tool ';
const String appDescriptionP3 =
    'to manage agents, track commissions, and streamline real estate operations.';
const String filterResultBannerText = 'Showing results for: ';

// haeder
const String dashboardHead = 'NeoRevv Dashboard';
const String notifications = 'Notifications';
const String settings = 'Settings';
const String sortByDefault = 'created_at';

// Login Screen
const String loginTitle = "Login";
const String signInWithGmail = "Sign in with Gmail";
const String signInWithApple = "Sign in with <PERSON>";
const String emailHint = "Enter your email";
const String passwordHint = "Enter your password";
const String rememberMe = "Remember me";
const String forgotPassword = "Forgot password?";
const String loginButton = "Login";
const String loggingIn = "Logging in...";
const String dontHaveAccount = "Don't have an account? ";
const String signUp = "Sign up";
const String orContinueWithEmail = "Or with email";
const String orContinueWith = "Or continue with";

// Dashboard
const String brokersTitle = "Brokerages";
const String searchHint = "Search";
const String brokerColumnHeader = "Brokerage Name";
const String contactsColumnHeader = "Contact";
const String emailAddressColumnHeader = "Email Address";
const String totalAgentsColumnHeader = "Total Agents";
const String joinDateColumnHeader = "Join Date";
const String totalSalesColumnHeader = "Total Sales";
const String actionsColumnHeader = "Actions";
const String viewDocumentsLabel = "View documents";
const String showingDataLabelP1 = "Showing data";
const String showingDataLabelP2 = "entries";
const String toLabel = "to";
const String ofLabel = "of";

const String dashboardTab = "Dashboard";
const String brokersTab = "Brokerages";
const String agentsTab = "Agents";
const String salesTab = "Sales";
const String commissionTab = "Commission";
const String reportsTab = "Reports";

const String revenueLabel = "Revenue";

const String addNewButton = "Add New";
const String welcomeLabel = 'Welcome, ';
const String platformOwnerLabel = 'Platform Owner';
const String copyright = 'Copyright © 2025 NeoRevv';
const String homeFooterLabel = 'Home';
const String privacyPolicy = 'Privacy Policy';
const String termsAndConditions = 'Terms and conditions';
const String viewDocuments = "View documents";
const String viewAgents = "View Agents";
const String relatedBrokerLabel = "Related Brokerage";
const String companyLabel = "Company";
const String revenueEarnedLabel = "Revenue Earned";

// Sales by Brokers Card
const String salesByBrokers = 'Sales by Brokerages';
const String selectMonthlabel = 'Select Month';
const String sales = 'Sales';
const String viewMore = 'View More';
const String viewLess = 'View Less';
const String filterBy = 'Filter by';
const String joiningDate = 'Joining Date';
const String showingResultsFor = 'Showing Results for ';
const String date = 'Date';
const String topPerformers = 'Top Performers';
const String viewAllBrokers = "view all brokerages";

const String grossCommission = "Gross Commission";
const String monthLabel = "Month";

//Sales Screen Table strings
const String salesTransactionIdColumnHeader = 'Transaction ID';
const String salesBrokerColumnHeader = 'Brokerage Name';
const String salesAgentColumnHeader = 'Agent Name';
const String salesPropertyTypeColumnHeader = 'Property Type';
const String salesPropertyAddressColumnHeader = 'Property Address';
const String salesPropertyValueColumnHeader = 'Property Value';
const String representingColumnHeader = 'Representing';
const String representingNameColumnHeader = 'Representing Name';
const String representingAddressColumnHeader = 'Representing Address';
const String salesListingDateColumnHeader = 'Listing Date';
const String salesDateColumnHeader = 'Sale Date';
const String salesAmountColumnHeader = 'Sale Price';
const String salesCommissionColumnHeader = 'Commission %';
const String salesCommissionAmtColumnHeader = ' Commission Amt';
const String salesReviewStatusColumnHeader = 'Review Status';
const String salesFormattedHeaders = 'formattedHeaders';
const String salesDataKey = 'salesData';
const String salesPropertyValueKey = 'propertyValue';
const String salesSalePriceKey = 'salePrice';
const String salesCommissionKey = 'commission';
const String salesCommissionAmtKey = 'commissionAmt';
const String salesDoc = 'Preview Closing Document';
const String closingDocUploadScreen = 'Upload Closing Document';

//Broker Listing Page
const String brokersDataKey = 'brokersData';
const String totalBrokerSalesRevenueKey = 'totalSalesRevenue';
const String totalBrokerCommissionKey = 'totalCommission';

const String brokerListNameColumnHeader = 'Brokerage Name';
const String brokerListContactColumnHeader = 'Contact';
const String brokerListEmailColumnHeader = 'Email Address';
const String brokerListAddressColumnHeader = 'Address';
const String brokerListJoinDateColumnHeader = 'Join Date';
// const String brokerListAgentsColumnHeader = 'Total Agents';
const String brokerListAgentsColumnHeader = 'Direct Recruits';
const String brokerListTotalSalesColumnHeader = 'Total Sales';
const String brokerListTotalRevenueColumnHeader = 'Total Revenue';
const String brokerListStateColumnHeader = 'State';
const String brokerListCityColumnHeader = 'City';
const String brokerListCommissionColumnHeader = 'Commission';
const String brokerListStatusColumnHeader = 'Status';

const String brokerListTotalAgentsKey = 'totalAgents';
const String brokerListtotalSalesVolume = 'totalSalesVolume';
const String brokerListTotalCommissionKey = 'totalCommission';
const String brokerListTotalSalesKey = 'totalSales';
const String brokerListJoinDateKey = 'joinDate';
const String brokerListImageUrlKey = 'imageUrl';
const String brokerListAgentsKey = 'totalAgents';
const String brokerListAddressKey = 'address';
const String brokerListIdKey = 'id';
const String brokerListNameKey = 'name';
const String brokerListContactKey = 'contact';
const String brokerListEmailKey = 'email';
// Agent
const String agents = "Agents";
const String agentName = "Agent Name";
const String agentContact = "Contact";
const String agentEmail = "Email Address";
const String agentJoinDate = "Join Date";
const String agentSoldHomes = "Sold Home";
const String agentState = "State";
const String agentCity = "City";
const String agentLevel = "Level";
const String agentRole = "Role";
const String agentTotalDeals = "Total Deals";
const String agentEarning = "Earning";
const String agentStatus = "Status";
const String actions = "Actions";
const String filter = "Filter";
const String active = "Active";
const String inactive = "Inactive";
const String searchAgent = "Search";
const String selectAgent = "Select Agent";
const String selectLevel = "Select Level";
const String selectStatus = "Select Status";
const String applyLabel = "Apply";
const String applyResetLabel = "Reset All";
const String agentFilter = "Agent";
const String filterResult = "Showing Results for ";
const String agentDataKey = "agentData";
const String agentRefferedBy = "Referred By";
const String agentTotalSales = "Total Sales";
const String agentCommission = "Commission";
const String agentTotalRevenue = "Total Revenue";
const String agentDirectRecruits = "Direct Recruits";
const String agentRelatedBrokerage = "Brokerage";

// Agent Screen Messages
const String pleaseLoginToAccessAgentData =
    "Please log in to access agent data";
const String authenticationRequired = "Authentication Required";
const String errorLoadingData = "Error Loading Data";
const String retry = "Retry";
const String actionClickedFor = "Action clicked for";

// Agent Role Constants
const String agentRoleValue = "1";

// HTTP Status and Error Constants
const String unauthorizedStatus = "401";
const String unauthorizedText = "Unauthorized";

// Formatting Constants
const String currencySymbol = "\$";
const String errorPrefix = "Error: ";

// Agent Network Hierarchy
const String agentNetworkScreen = "Agent Network";
const String agentHierarchy = "Agent Hierarchy";
const String agentNetwork = "Agent Network";
const String totalSalesRevenue = "Total Sales Revenue";
const String totalCommission = "Total Commission";
const String recruitsCount = "Recruits Count ";
const String recruitsMobile = "Recruits ";
const String viewProfile = "View Profile";
const String agentsRecruitedBy = "Agents recruited by";
const String brokerLabel = "Brokerage";
const String agentLabel = "Agent";
const String officeStaffLabel = "Office Staff";
const String noRecruitsFound = "No recruits found";
const String hasNotRecruitedAnyAgentsYet = "hasn't recruited any agents yet.";
const String noBrokerSelected = "No brokerage selected";

// Breadcrumb
const String dashboardAdmin = 'Dashboard';
const String addNewBroker = 'Add New Brokerage';
const String reviewClosingDocument = 'Review Closing Document';
const String editClosingDocument = 'Edit Closing Document';

// Broker Registration Screen
const String brokerInformation = 'Brokerage Information';
const String firstName = 'First Name';
const String lastName = 'Last Name';
const String phone = 'Phone';
const String email = 'Email';
const String company = 'Company';
const String city = 'City';
const String stateProvince = 'State/Province';
const String postalZipCode = 'Postal/Zip Code';
const String country = 'Country';
const String enterFirstName = 'Enter your first name';
const String enterLastName = 'Enter your last name';
const String enterPhone = 'Enter your phone number';
const String enterEmail = 'Enter your email address';
const String enterCompany = 'Enter your company name';
const String uploadDocuments = 'Upload Documents';
const String eoInsuranceCertificate =
    'E&O Insurance Certificate (Errors and Omissions)';
const String brokerageLicense = 'Brokerage License';
const String principalBrokerId = 'Principal Brokerage ID';
const String logo = 'Logo';
const String chooseFileOrDragDrop = 'Choose a file';
const String pdfOrImageOnly = 'PDF or image formats only. Max 25 MB.';
const String chooseImageOrDragDrop = 'Choose image';
const String imageFormatsOnly = 'JPG, JPEG, PNG and WEBP. Max 25 MB.';
const String clear = 'Clear';
const String register = 'Register';
const String inviteBroker = 'Invite';
const String inviteBrokerage = 'Invite Brokerage';
const String registerBroker = 'Register Brokerage';
const String registerAgent = 'Register Agent';
const String inviteRegisterAgent = 'Invite Agent';
const String registerNewBroker = 'Add New Brokerage';
const String registerNewAgent = 'Add New Agent';
const String registerStaff = 'Register Staff';
const String inviteAgent = 'Invite Agent';
const String signupAgent = 'SIGNUP';
const String inviteStaff = 'Invite Ofice Staff';
const String agentInformation = 'Agent Information';
const String staffInformation = 'Office staff Information';
const String agentLicenseId = 'Agent License ID';
const String referralCode = 'Referral Code';
const String enterAgentLicenseId = 'Enter your License Number';
const String enterReferralCode = 'Enter your referral code';
const String uploadAgentLicenseId = 'Upload Agent License ID';
const String uploadStaffId = 'Upload ID';
const String additionalInformation = 'Additional Information';
const String upload = 'Upload';
const String postalCodeEg = 'e.g., 79916-0954';

const String switchTab = 'Switch Tab';
const String switchTabConfirmation =
    'Are you sure you want to switch tabs? All the entered data in the form will be lost.';
const String clearData = 'Clear Data';
const String clearDataConfirmation =
    'Are you sure you want to clear the form? All entered data will be lost, and you will need to re-enter it.';
const String cancel = 'Cancel';
const String ok = 'OK';
const String processingData = 'Processing Data...';

// Validation
const String thisFieldIsRequired = 'This field is required';
const String phoneNumberIsRequired = 'Phone number is required';
const String emailIsRequired = 'Email is required';
const String invalidEmail = 'Please enter a valid email address';
const String invalidPhone = 'Please enter a valid 10-digit phone number';
const String phoneNumberExceed = 'Phone number must be exactly 10 digits';
const String pleaseFillRequiredFields =
    'Please fill all required fields correctly';
const String passwordIsRequired = 'Password is required';
const String passwordMustBeAtLeast6Characters =
    'Password must be at least 8 characters';
const String passwordsDoNotMatch = 'Passwords do not match';
const String postalCodeIsRequired = 'Postal code is required';
const String invalidCommissionSplitFormat =
    'Invalid commission split format. \nValue must be a number between 0 and 100';
const String invalidCommissionSplitDecimalPlaces =
    'Invalid commission split format. \nMaximum 2 decimal places allowed';
const String invalidCurrencyFormat = 'Invalid currency format';
const String decimalsNotAllowed = 'Decimals are not allowed for this field.';
const String maximum2DecimalPlacesAllowed =
    'Maximum 2 decimal places allowed for this field.';

// Edit Closing Document
const String representing = "Representing:";
const String buyer = "Buyer";
const String seller = "Seller";
const String transactionId = "Transaction ID";
const String transactionName = "Transaction Name";
const String address = "Address";
const String salesVolume = "Sales Volume";
const String moneyReceived = "Money Received(Deposit)";
const String dateDeposited = "Date Deposited";
const String saleType = "Sale Type:";
const String traditional = "Traditional";
const String lease = "Lease";
const String commercial = "Commercial";
const String commissionSplit = "Commission %";
const String dateReleased = "Date Released";
const String listingDate = "Listing Date";
const String expirationDate = "Expiration Date(of Listing)";
const String closingDate = "Closing Date";
const String legalDescription = "Legal Description";
const String escrowNumber = "Escrow Number";
const String dateReceived = "Date Received";
const String amountReleased = "Amount Released";
const String representedContact = "Represented Contact";
const String traditionalOptions = "TRADITIONAL";
const String leaseOption = "LEASE";
const String commercialOption = "COMMERCIAL";

const String companyPhoneNumber = "Company Phone Number";
const String contactAddress = "Contact Address";
const String leadSource = "Lead Source";
const String uploadBrokerSignature = "Upload Brokerage Signature";
const String save = "Save";
const String uploadText = "Choose a file.";
const String uploadFormat = "Image formats only. Max 1 MB";
const String submit = "Submit";
const String or = "Or";
const String saleAgreement = "Sale Agreement";
const String closingDocument = "Closing Document";
const String signedClosingDocument = "Signed Closing Document";

const String salesAgreement = 'Sales Agreement';
const String documentFileName = 'The document file.pdf';
const String view = 'View';
const String download = 'Download';
const String edit = 'Edit';
const String uploadSignature = 'Upload Signature';
const String saleTypeLabel = 'Sale Type';
const String reviewerDetails = 'Reviewer Details';

const String commissionContent = "Commission Content";
const String reportsContent = "Reports Content";
//calender strings
const String quickSelection = 'Quick Selection';
const String selectDateRange = 'Select Date';
const String selectEndDate = 'Select end date';
const String selectStartDate = 'Select start date';

/// Creating agent
const String agentCreatedSuccessfully =
    'Registration complete. The invited agent will get an email with their referral code. Please ask them to check their inbox and follow the link to join your team.';
const String staffCreatedSuccessfully = 'User created successfully!';
const String fileUploadFailed = 'File upload failed:';
const String invalidFile = 'Invalid file: Unable to read file data';
// const String agentCreatedSuccessfullyWithUploadPrompt =
//     'Invitation sent successfully! Check your email.';
const String staffCreatedSuccessfullyWithUploadPrompt =
    'User created successfully! Please upload the license file.';
const String fileSizeKB = 'KB';
const String photoLibrary = 'Photo Library';
const String camera = 'Camera';
const String files = 'Files';
const String agentRegisteredSuccessfully = 'Agent registered successfully';
const String failedToUploadAgentInviteStatusUserIDEmpty =
    'Failed to update agent invite status. User ID is empty.';
const String failedToUploadBrokerageRegStatusUserIDEmpty =
    'Failed to update brokerage registration status. User ID is empty.';

const String addNewBrokerage = 'Add New Brokerage';
const String addNewAgent = 'Add New Agent';
const String addNewStaff = 'Add New Staff';

const String changePasswordTitle = 'Change Password';
const String username = 'Username';
const String enterEmailAddress = 'Enter email address';
const String pleaseEnterEmail = "Please enter email";
const String password = "Password";
const String enterPassword = "Enter password";
const String pleaseEnterPassword = "Please enter password";
const String confirmPassword = "Confirm Password";
const String reEnterPassword = "Re-enter new password";
const String pleaseConfirmPassword = "Please confirm password";
const String proceed = "Proceed";
const String proceeding = "Proceeding...";

const String passwordResetSuccess = 'Password created successfully';

const String emailVerificationSuccess = 'Email verification successful';
const String emailVerificationFailure = 'Email verification failed';
const String verifyEmail = 'Forgot Password?';
const String sendInviteLink = "Send Reset Link";
const String pleaseEnterYourEmailAddress =
    'Enter your email address and we’ll send you instructions to\nreset your password.';
const String enterNewPassword = "Enter new password";
const String newPassword = "New Password";
const String enterNewLoginCredentials = "Create new password";
const String resetYourLoginCredentials = 'Reset Your Login Credentials';
const String createYourLoginCredentials = 'Create Your Login Credentials';
const String oldPassword = "Current Password";
const String enterOldPassword = "Enter current password";
const String forgotPasswordResetSuccess = 'Password reset successful';
const String emailAddressHead = 'Email Address';
const String backToLogin = 'Return back to login page';
const String resetPassword = "Reset Password";
const String changePassword = "Change Password";
const String backToHome = 'Back to Home';

/// Validation
const String pleaseUploadLicence = 'Please upload the license file.';

///
/// Errors/ Exceptions
///
const String invalidCredentials = "Invalid username or password";
const String loginFailed = "Login failed";
const String failedToFetchUserProfile = "Failed to fetch user profile";
const String userNotFound = "User not found";
const String unexpectedError = "An unexpected error occurred";
const String internalServerError = "Internal Server Error";
const String noDataAvailable = 'No data available';
const String failedToRegisterBroker = "Failed to register brokerage";
const String failedToRegisterBrokerDueToUnauthorizedRequest =
    "Brokerage registration failed due to unauthorized request.";
const String registrationSuccess =
    "Brokerage registration successful. Please inform the brokerage to check their email for the password setup link to access the platform.";
const String invalidRefreshToken = 'Invalid refresh token';
const String failedToRefreshToken = 'Failed to refresh token';
const String badRequest = 'Bad request';
const String failedToFetchBrokers = 'Failed to fetch brokerages';
const String failedToFetchAgentDetails = 'Failed to fetch agent details';
const String failedToFetchAgents = 'Failed to fetch agents';
const String failedToFetchBrokerageTopPerformers =
    "Failed to fetch brokerage top performers";
const String failedToFetchAgentTopPerformers =
    "Failed to fetch agent top performers";
const String errorPickingFile = 'Error picking file';
const String errorFetchingFilterOptions = 'Error fetching filter options';
const String filePathNull = 'File path is null, cannot upload file';
const String fileUploadFailedUnauthorizedRequest =
    'Failed to upload file due to unauthorized request';
const String brokerRegisterSuccess = 'Brokerage registered successfully';
const String failedToPickImageFromGallery = 'Failed to pick image from gallery';
const String failedToCaptureImage = 'Failed to capture image';
const String pleaseSelectValidFileType = 'Please select a valid file type:';
const String failedToOpenFilePicker = 'Failed to open file picker';
const String failedToCreateAgent = 'Failed to create agent';
const String failedToCreateStaff = 'Failed to create user';
const String failedToFetchSalesDetails = "Failed to fetch sales details";
const String failedSocialSignin = "sign-in failed";
const String signup = 'SIGNUP';
const String brokerageReferalCode = 'Brokerage Referral Code';
const String referalCode = 'Referral Code';
const String registerOfficeStaff = 'Register Office Staff';
const String addOfficeStaff = 'Add Office Staff';
const String fileUploadSuccsess = 'File uploaded successfully';
const String pleaseSelectRole = 'Please select a role';
const String selectRole = 'Select role';
const String customFileTypeFailed =
    'Custom file type failed, trying FileType.any';
const String filePickerResult = 'File picker result';
const String staffFiles = 'files';
const String invalidResponseFormat = 'Invalid response format';
const String failedToUploadStaffFile = 'Failed to upload user file';
const String brokerRegisterSuccessWithoutFiles =
    'Brokerage registered successfully.';
const String brokerRegisterStatusFailure =
    'Brokerage registeration failed. Please try again';
const String agentRegisterStatusFailure =
    'Agent registeration failed. Please try again';
//filter exception strings
const String noDataFound = 'No Item Found!';

const String connectionResetByPeer = 'Connection reset by Peer';
const String requestEntityTooLarge = 'Request Entity Too Large';
// API Error Handler Messages
const String badRequestError = 'Bad Request';
const String unauthorizedError = 'Unauthorized';
const String forbiddenError = 'Forbidden';
const String notFoundError = 'Not Found';
const String requestTimeoutError = 'Request Timeout';
const String conflictErrorPrefix = 'Conflict: ';
const String validationFailedPrefix = 'Validation Failed: ';
const String tooManyRequestsError = 'Too Many Requests';
const String internalServerErrorMessage = 'Internal Server Error';
const String badGatewayError = 'Bad Gateway';
const String serviceUnavailableError = 'Service Unavailable';
const String gatewayTimeoutError = 'Gateway Timeout';
const String unknownError = 'Unknown error';

const String profile = 'Profile';
const String next = 'Next Page';
const String previous = 'Previous Page';
const String page = 'Page';
const String hideFilters = 'Hide Filters';
const String showFilters = 'Show Filters';
const String closeFilters = 'Close Filter Panel';
const String resetFilters = 'Reset Filters';
const String menu = 'Menu';
const String delete = 'Delete';
const String applyFilters = 'Apply Filters';
const String gallery = 'Gallery';
const String previousYear = 'Previous Year';
const String nextYear = 'Next Year';
const String selectMonthYear = 'Select Month & Year';
const String select = "Select";
const String previousMonth = 'Previous Month';
const String nextMonth = 'Next Month';
const String changeYear = 'Change Month & Year';
const String viewAllUpstreamAgents = 'View all upstream agents';
const String enterValidCountryName =
    'Enter a valid country name \n(letters, spaces, - and \' only)';
const String invite = 'Invite';

//Logout
const String confirmLogout = 'Confirm Logout';
const String logoutConfirmation = 'Are you sure you want to log out?';
const String yes = 'Yes';
const String no = 'No';
const String enterCity = "Enter city";
const String enterState = "Enter state/province";
const String enterCountry = "Enter country";
const String whiteSpaceValidation =
    "Please enter a valid value (whitespaces alone are not allowed)";
const String minimize = 'Minimize';
const String brokerageSignature = "Brokerage Signature:";
const String previewPdf = "Preview PDF";
const String pdfPreview = "PDF Preview";
const String salesReviewDocument = "Sales Review Document";
const String mobilePreviewNotAvailable =
    "Preview not available on mobile.\nUse download to view the PDF.";
const String documentRepresenting = "Document Representing:";
const String noSignatureProvided = "No signature\nprovided";
const String dates = "Date:";
const String sourceUploaded = "Source: Uploaded";
const String sourceDrawn = "Source: Drawn";
const String salesReviewDocumentPrefix = "sales_review_document_";
const String pdfDownloadInitiated = "PDF download initiated";
const String pdfDownloadedSuccessfully = "PDF downloaded successfully";
const String pdfSavedTo = "PDF saved to:";
const String errorDownloadingPdf = "Error downloading PDF:";
const String previewImage = "preview.png";
const String downloadImage = "download.png";
const String errorGeneratingPDF = "Error generating PDF";
const String errorGeneratingPDFPreview = "Error generating PDF preview:";
const String pdfPreviewFileName = "pdf-preview-";
const String signupButton = "Signup";

// Upload Document Screen
const String uploadClosingDocument = 'Upload Closing Document';
const String uploadFile = 'Upload File';
const String selectAndUploadFile = 'Upload a PDF file (up to 25 MB)';
const String browseFile = 'Browse File';
const String chooseAnotherFile = 'Choose Another File';
const String chooseDifferentFileOrDragDrop = 'Choose a different file.';
const String pdfFormatInfo = 'PDF format, up to 25 MB.';
const String uploadAndContinue = 'Upload & Continue';
const String uploading = 'Uploading...';
const String downloading = 'Start Downloading...';

// File info
const String fileSize = '60 kB of 120 kB';

// Alert messages
const String fileSizeExceededTitle = 'File Size Exceeded';
const String fileSizeExceededMessage =
    'The selected file exceeds the maximum allowed size of 25 MB. Please choose a smaller file.';
const String okButton = 'OK';
const String uploadFileSuccess = 'File uploaded successfully';
const String errorOccured = 'Error occure during file upload';
const String selectPdfFile = 'Please select a PDF file.';
const String errorSubmittingForm = 'Error submitting form:';
const String uploadCompletedSuccessfully = 'Upload completed successfully';
const String uploadFailed = 'Upload failed';
const String pleaseEnterValidDate = 'Please select ';

const String preparingUpload = "Preparing upload...";
const String uploadingDocument = "Uploading document...";
const String processing = "Processing...";
const String uploadCompleteProcessing = "Upload complete, processing...";
const String finalizingUpload = "Finalizing upload...";
const String readyToUpload = "Ready to upload";
const String fileSizeExceeded = "File size must be less than 25MB";

// Tabs
const String resetPasswordTab = 'Reset Password';
const String brokerageReviewed = 'BROKERAGE_REVIEWED';
const String agentReviewed = 'AGENT_REVIEWED';
const String pending = 'PENDING';
const String pleaseSignTheDocument = 'Please sign the document';
const String pleaseSelectRepresentingValues =
    'Please select Representing, Transaction Type and Sale Type';
const String pleaseSelectSaleTypeValues = 'Please select Sales Type';
const String fileTooLarge = 'File too large. Max size is 1 MB.';
const String selectSignature = 'Select Signature';
const String pleaseSelectOneOfTheSignaturesBelow =
    'Please select one of the signatures below. The chosen signature will appear in the closing document.';
const String invalidImageType =
    'Unsupported file format. Please upload a valid image file. Allowed types:';
const String invalidSignatureFile =
    'Invalid file. Please upload a valid signature Document.';
const String corruptedImage = 'Selected image is corrupted';

const String continueText = 'Continue';
const String pleaseSelectStateFirst = 'Please select state first';

const String reviewDocCancelTitle = 'Are You Sure You Want to Cancel?';
const String reviewDocCancelContent = 'Changes will not be saved';
const String failedToFetchTransactionTypes =
    "Failed to fetch transaction types";
const String failedToFetchRepresentingTypes =
    "Failed to fetch representing types";
const String failedToFetchLeadSourceTypes = "Failed to fetch lead source types";

const String appleEmailWarningContent =
    '''This application requires your verified email address to create and maintain your account.

If you select “Hide My Email” during Apple Sign-In, we will not receive the required information to complete your registration, and you will be unable to access the service.

By proceeding, you acknowledge that your email will be used solely for account identification and essential communication.
 ''';

const String appleEmailWarningTitle = 'Apple Sign-In Notice';
const String fileSizeLager =
    "Processing your upload… this may take a few moments, please wait.";
const String processingInitiated = "Processing initiated…";
const String analyzingFile = "Analyzing your file…";
const String optimizingUpload = "Optimizing upload…";
const String almostDone = "Almost done, finalizing…";
const String reviewAndEditClosingDocumentData = "Review & Edit Closing Document Data";
