import 'package:flutter/services.dart';

class CommissionSplitFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    String text = newValue.text;

    // Allow empty string (so user can delete)
    if (text.isEmpty) return newValue;

    // Only allow digits + optional single decimal point
    final validChars = RegExp(r'^\d*\.?\d*$');
    if (!validChars.hasMatch(text)) {
      return oldValue;
    }

    // Limit decimals to 2 places
    if (text.contains('.')) {
      final parts = text.split('.');
      if (parts.length > 2 || parts[1].length > 2) {
        return oldValue;
      }
    }

    // Parse number safely
    final value = double.tryParse(text);
    if (value == null) return oldValue;

    // Prevent >100
    if (value > 100) {
      return oldValue;
    }

    return newValue;
  }
}
