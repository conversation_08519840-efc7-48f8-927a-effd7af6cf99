import '../config/app_strings.dart';
import 'regex.dart';

class InputValidators {
  InputValidators._();

  static String? validateEmail(String? value) {
    if (value == null || value.isEmpty) return emailIsRequired;
    final emailRegex = RegExUtils.emailRegex;
    if (!emailRegex.hasMatch(value)) return invalidEmail;
    return null;
  }

  static String? validatePhone(String? value) {
    if (value == null || value.isEmpty) {
      return phoneNumberIsRequired;
    }
    // US phone validation: 10 digits (excluding country code)
    // Allow formats like: 1234567890, ************
    final cleanedNumber = value.replaceAll(RegExUtils.nonNumeric, '');
    if (cleanedNumber.length != 10) {
      return invalidPhone;
    }
    if (value.length != 10) {
      return phoneNumberExceed;
    }
    return null;
  }

  static String? validateCompanyPhone(String? value) {
    if (value == null || value.isEmpty) {
      return phoneNumberIsRequired;
    }

    // Remove all non-digits (ignores '-', spaces, etc.)
    final cleanedNumber = value.replaceAll(RegExUtils.nonNumeric, '');

    // Must be exactly 10 digits
    if (cleanedNumber.length != 10) {
      return invalidPhone;
    }

    return null;
  }

  static String? validatePassword(String? value) {
    if (value == null || value.isEmpty) return passwordIsRequired;
    if (value.length < 8) return passwordMustBeAtLeast6Characters;
    return null;
  }

  static String? validateConfirmPassword(String? value, String password) {
    if (value != password) return passwordsDoNotMatch;
    return null;
  }

  static String? validateRequiredField(String? value) {
    if (value == null || value.isEmpty) return thisFieldIsRequired;
    return null;
  }

  static String? validateZipCode(String? value) {
    if (value == null || value.trim().isEmpty) return postalCodeIsRequired;
    final zipPattern = RegExUtils.zipCodeRegex;
    if (!zipPattern.hasMatch(value.trim())) {
      return 'Invalid zip code format. \neg: 72546-4567 or 72546';
    }
    return null;
  }

  static String? validateTextLengthRange(
    String? value, {
    String fieldLabel = 'Name',
    int limit = 50,
  }) {
    if (value == null || value.trim().isEmpty) return '$fieldLabel is required';
    final trimmed = value.trim();
    if (trimmed.length < 1 || trimmed.length > limit) {
      return '$fieldLabel \nlength must be 1-$limit';
    }
    return null;
  }

  static String? validateCommissionSplit(String? value) {
    if (value == null || value.trim().isEmpty) {
      return invalidCommissionSplitFormat;
    }

    final text = value.trim();

    // Try parsing to number
    final numValue = double.tryParse(text);
    if (numValue == null) {
      return invalidCommissionSplitFormat;
    }

    // Check range
    if (numValue < 0 || numValue > 100) {
      return invalidCommissionSplitFormat;
    }

    // Check decimal precision (max 2 digits)
    if (text.contains('.')) {
      final decimals = text.split('.')[1];
      if (decimals.length > 2) {
        return invalidCommissionSplitDecimalPlaces;
      }
    }

    return null;
  }

  static String? validateCurrency(
    String? value, {
    bool allowDecimal = true, // toggle decimal check
  }) {
    if (value == null || value.trim().isEmpty) return thisFieldIsRequired;
    final text = value.trim();

    // Try parsing to number
    final numValue = double.tryParse(text);
    if (numValue == null) {
      return invalidCurrencyFormat;
    }

    if (!allowDecimal) {
      // Reject if contains decimal
      if (text.contains('.')) {
        return decimalsNotAllowed;
      }
    } else {
      // If decimal is allowed, max 2 digits after decimal
      if (text.contains('.')) {
        final decimals = text.split('.')[1];
        if (decimals.length > 2) {
          return maximum2DecimalPlacesAllowed;
        }
      }
    }

    return null;
  }
}
