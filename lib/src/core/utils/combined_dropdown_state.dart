import '../../domain/models/filter/table_filter.dart';


// Helper class to combine two ValueListenables following Flutter best practices
class CombinedDropdownState {
  final List<TableFilter> options;
  final TableFilter? selected;

  const CombinedDropdownState(this.options, this.selected);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CombinedDropdownState &&
          runtimeType == other.runtimeType &&
          options == other.options &&
          selected == other.selected;

  @override
  int get hashCode => options.hashCode ^ selected.hashCode;
}