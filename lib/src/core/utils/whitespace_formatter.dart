import 'package:flutter/services.dart';

class WhitespaceFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    String text = newValue.text;

    // Block leading whitespace
    if (text.startsWith(' ')) {
      text = text.trimLeft();
    }

    // Replace multiple consecutive spaces with a single space
    text = text.replaceAll(RegExp(r'\s+'), ' ');

    // Adjust the cursor position
    int baseOffset = newValue.selection.baseOffset;
    int extentOffset = newValue.selection.extentOffset;
    int diff = newValue.text.length - text.length;
    int newBaseOffset = (baseOffset - diff).clamp(0, text.length);
    int newExtentOffset = (extentOffset - diff).clamp(0, text.length);

    return TextEditingValue(
      text: text,
      selection: TextSelection(
        baseOffset: newBaseOffset,
        extentOffset: newExtentOffset,
      ),
    );
  }
}
