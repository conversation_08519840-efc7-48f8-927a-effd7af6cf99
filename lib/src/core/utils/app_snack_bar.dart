import 'package:flutter/material.dart';

import '../theme/app_theme.dart';

enum SnackBarType { success, error, warning, info }

class AppSnackBar {
  const AppSnackBar._();

  static Future<void> showSnackBar(
    BuildContext context,
    String message,
    SnackBarType type, {
    Duration? timeout = const Duration(seconds: 2),
  }) async {
    await ScaffoldMessenger.of(context)
        .showSnackBar(
          SnackBar(
            content: Text(message),
            backgroundColor: _getBackgroundColor(type),
            duration: timeout ?? const Duration(seconds: 2),
          ),
        )
        .closed;
  }

  static Color _getBackgroundColor(SnackBarType type) {
    switch (type) {
      case SnackBarType.success:
        return AppTheme.successColor;
      case SnackBarType.error:
        return AppTheme.errorColor;
      case SnackBarType.warning:
        return AppTheme.warningColor;
      case SnackBarType.info:
        return Colors.blue;
    }
  }
}
