class PhoneUtils {
  static String formatPhoneNumber(String phoneNumber) {
    if (phoneNumber.isEmpty) return phoneNumber;
    
    // Remove any spaces, dashes, parentheses, or other formatting, but keep the +
    String cleanNumber = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');
    
    // If it already starts with +1, extract the digits and reformat
    if (cleanNumber.startsWith('+1')) {
      String digits = cleanNumber.substring(2); // Remove +1
      if (digits.length == 10) {
        return '+1-${digits.substring(0, 3)}-${digits.substring(3, 6)}-${digits.substring(6)}';
      }
    }
    
    // If it starts with 1 and has 11 digits total
    if (cleanNumber.startsWith('1') && cleanNumber.length == 11) {
      String digits = cleanNumber.substring(1); // Remove the leading 1
      if (digits.length == 10) {
        return '+1-${digits.substring(0, 3)}-${digits.substring(3, 6)}-${digits.substring(6)}';
      }
    }
    
    // If it's a 10-digit number, add +1 and format
    if (cleanNumber.length == 10) {
      return '+1-${cleanNumber.substring(0, 3)}-${cleanNumber.substring(3, 6)}-${cleanNumber.substring(6)}';
    }
    
    // Return original if it doesn't match expected patterns
    return phoneNumber;
  }
}