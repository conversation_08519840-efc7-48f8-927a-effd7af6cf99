abstract class AuthRepository {
  Future<dynamic> login(Map<String, dynamic> payload);
  Future<dynamic> createPassword(Map<String, dynamic> payload);
  Future<dynamic> signInWithGoogle(Map<String, dynamic> payload);
  Future<dynamic> signInWithApple(Map<String, dynamic> payload);
  Future<dynamic> verifyEmail(Map<String, dynamic> payload);
  Future<dynamic> validateToken(String token);
  Future<dynamic> forgotPassword(Map<String, dynamic> payload);
  Future<dynamic> resetPassword(Map<String, dynamic> payload);
  Future<bool> logout();
}
