import 'package:neorevv/src/domain/models/agent.dart';
import 'package:neorevv/src/domain/models/agent_network_model.dart';
import 'package:neorevv/src/domain/models/upstream_model.dart';

import '/src/domain/models/info_card_item_model.dart';

abstract class GetNetworkItemRepository {
  Future<AgentNetworkModel> getNetworkItem(String url, String userId);
  Future<List<UpStreamModel>> getUpstreamUsers(String url, String userId);
}
