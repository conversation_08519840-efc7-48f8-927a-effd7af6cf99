import 'package:neorevv/src/domain/models/lead_source_model.dart';
import 'package:neorevv/src/domain/models/representing_type_model.dart';
import 'package:neorevv/src/presentation/screens/sales/sales_review_doc_screen.dart';

import '../models/sales.dart';
import '../models/sales_details.dart';
import '../models/transaction_type_model.dart';

abstract class SalesDetailsRepository {
  Future<SalesDetailsApi?> getSalesDetails(Map<String, dynamic> payload);
  Future<SalesClosingDocDetailsApi?> getSalesClosingDocumentDetails(
    String salesID,
    String requestingUserId,
  );
  Future<dynamic> uploadSalesClosingDocumentFile(
    Map<String, dynamic> requestBody,
  );
  Future<dynamic> editSalesClosingDocument(
    Map<String, dynamic> requestBody,
    String salesID,
  );
  Future<dynamic> updateSalesClosingDocUploadStatus(
    String salesId,
    Map<String, dynamic> requestBody,
  );
  Future<dynamic> updateSalesClosingDocCommissionUpdate(
    String salesId
  );
  Future<List<TransactionType>> getTransactionTypes();
  Future<List<RepresentingTypes>> getRepresentingTypes();
  Future<List<LeadSource>> getLeadSourceTypes();
}
