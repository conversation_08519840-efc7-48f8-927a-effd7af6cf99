
import '/src/core/utils/date_formatter.dart';

import '../../core/utils/helper.dart';

class AgentModel {
  final String userId;
  final String firstName;
  final String lastName;
  final String fullName;
  final String email;
  final String phone;
  final String state;
  final String city;
  final String depthLevel;
  final String companyName;
  final DateTime createdAt;
  final DateTime? joiningDate;
  final int totalDownlineAgents;
  final double totalRevenue;
  final int totalDownlineSales;
  final int totalSales;
  final double commission;
  final String recruiterName;
  final String associatedBrokerage;
  final bool isActive;

  AgentModel({
    required this.userId,
    required this.firstName,
    required this.lastName,
    required this.fullName,
    required this.email,
    required this.phone,
    required this.state,
    required this.city,
    required this.companyName,
    required this.createdAt,
    required this.joiningDate,
    required this.totalDownlineAgents,
    required this.totalRevenue,
    required this.totalDownlineSales,
    required this.commission,
    required this.associatedBrokerage,
    required this.depthLevel,
    required this.isActive,
    required this.recruiterName,
    required this.totalSales,
  });

  factory AgentModel.fromJson(Map<String, dynamic> json) {
    return AgentModel(
      userId: json['userId']?.toString() ?? '',
      firstName: json['firstName']?.toString() ?? '',
      lastName: json['lastName']?.toString() ?? '',
      fullName: json['fullName']?.toString() ?? '',
      email: json['email']?.toString() ?? '',
      phone: json['phone']?.toString() ?? '',
      state: json['state']?.toString() ?? '',
      city: json['city']?.toString() ?? '',
      createdAt: parseDate(json['createdAt']),
      joiningDate: AppDateFormatter.formatMonthYearJson(json['joiningDate']),
      totalDownlineAgents: toInt(json['totalDownlineAgents']),
      totalRevenue: toDouble(json['totalRevenue']),
      totalDownlineSales: toInt(json['totalDownlineSales']),
      totalSales: toInt(json['totalSales']),
      commission: toDouble(json['commission']),
      associatedBrokerage:
          json['associatedBrokerageFullName']?.toString() ?? '',
      depthLevel: json['depthLevel']?.toString() ?? '',
      isActive: json['isActive'] as bool? ?? false,
      recruiterName: json['recruiterName']?.toString() ?? '',
      companyName: json['companyName']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'firstName': firstName,
      'lastName': lastName,
      'fullName': fullName,
      'email': email,
      'phone': phone,
      'state': state,
      'city': city,
      'companyName': companyName,
      'recruiterName': recruiterName,
      'createdAt': createdAt.toIso8601String(),
      'joiningDate': joiningDate?.toIso8601String(),
      'totalDownlineAgents': totalDownlineAgents,
      'totalRevenue': totalRevenue,
      'totalDownlineSales': totalDownlineSales,
      'totalSales': totalSales,
      'commission': commission,
      'associatedBrokerageFullName': associatedBrokerage,
      'depthLevel': depthLevel,
      'isActive': isActive,
    };
  }

  @override
  String toString() {
    return 'AgentModel(id: $userId, fullName: $fullName, email: $email, level: $depthLevel)';
  }
}

class ApiAgentResponse {
  final List<AgentModel> content;
  final Pageable pageable;
  final int totalElements;
  final int totalPages;
  final bool last;
  final int size;
  final int number;
  final Sort sort;
  final int numberOfElements;
  final bool first;
  final bool empty;

  ApiAgentResponse({
    required this.content,
    required this.pageable,
    required this.totalElements,
    required this.totalPages,
    required this.last,
    required this.size,
    required this.number,
    required this.sort,
    required this.numberOfElements,
    required this.first,
    required this.empty,
  });

  factory ApiAgentResponse.fromJson(Map<String, dynamic> json) {
    return ApiAgentResponse(
      content:
          (json['content'] as List<dynamic>?)
              ?.map((item) => AgentModel.fromJson(item as Map<String, dynamic>))
              .toList() ??
          [],
      pageable: Pageable.fromJson(
        json['pageable'] as Map<String, dynamic>? ?? {},
      ),
      totalElements: toInt(json['totalElements']),
      totalPages: toInt(json['totalPages']),
      last: json['last'] as bool? ?? false,
      size: toInt(json['size']),
      number: toInt(json['number']),
      sort: Sort.fromJson(json['sort'] as Map<String, dynamic>? ?? {}),
      numberOfElements: toInt(json['numberOfElements']),
      first: json['first'] as bool? ?? false,
      empty: json['empty'] as bool? ?? true,
    );
  }
}

class Pageable {
  final int pageNumber;
  final int pageSize;
  final Sort sort;
  final int offset;
  final bool paged;
  final bool unpaged;

  Pageable({
    required this.pageNumber,
    required this.pageSize,
    required this.sort,
    required this.offset,
    required this.paged,
    required this.unpaged,
  });

  factory Pageable.fromJson(Map<String, dynamic> json) {
    return Pageable(
      pageNumber: toInt(json['pageNumber']),
      pageSize: toInt(json['pageSize']),
      sort: Sort.fromJson(json['sort'] as Map<String, dynamic>? ?? {}),
      offset: toInt(json['offset']),
      paged: json['paged'] as bool? ?? false,
      unpaged: json['unpaged'] as bool? ?? false,
    );
  }
}

class Sort {
  final bool sorted;
  final bool unsorted;
  final bool empty;

  Sort({required this.sorted, required this.unsorted, required this.empty});

  factory Sort.fromJson(Map<String, dynamic> json) {
    return Sort(
      sorted: json['sorted'] as bool? ?? false,
      unsorted: json['unsorted'] as bool? ?? false,
      empty: json['empty'] as bool? ?? true,
    );
  }
}
