import 'dart:convert';

List<TableFilter> tableFilterFromJson(String str) => List<TableFilter>.from(
  json.decode(str).map((x) => TableFilter.fromJson(x)),
);

String tableFilterToJson(List<TableFilter> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class TableFilter {
  String id;
  dynamic key;
  String value;

  TableFilter({required this.id, required this.key, required this.value});

  TableFilter copyWith({String? id, dynamic key, String? value}) => TableFilter(
    id: id ?? this.id,
    key: key ?? this.key,
    value: value ?? this.value,
  );

  factory TableFilter.fromJson(Map<String, dynamic> json) =>
      TableFilter( id: (json["id"] ?? '').toString(), key: json["key"], value: json["value"]);

  Map<String, dynamic> toJson() => {"id": id, "key": key, "value": value};
}
