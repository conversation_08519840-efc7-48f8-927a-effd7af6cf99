// To parse this JSON data, do
//
//     final transactionType = transactionTypeFrom<PERSON>son(jsonString);

import 'dart:convert';

List<TransactionType> transactionTypeFromJson(String str) => List<TransactionType>.from(json.decode(str).map((x) => TransactionType.fromJson(x)));

String transactionTypeToJson(List<TransactionType> data) => json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class TransactionType {
    String? id;
    String? key;
    String? value;

    TransactionType({
        this.id,
        this.key,
        this.value,
    });

    TransactionType copyWith({
        String? id,
        String? key,
        String? value,
    }) => 
        TransactionType(
            id: id ?? this.id,
            key: key ?? this.key,
            value: value ?? this.value,
        );

    factory TransactionType.fromJson(Map<String, dynamic> json) => TransactionType(
        id: json["id"],
        key: json["key"],
        value: json["value"],
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "key": key,
        "value": value,
    };
}
