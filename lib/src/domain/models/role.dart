// To parse this JSON data, do
//
//     final role = roleFrom<PERSON><PERSON>(jsonString);

import 'dart:convert';

List<Role> roleFromJson(String str) =>
    List<Role>.from(json.decode(str).map((x) => Role.fromJson(x)));

String roleToJson(List<Role> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class Role {
  String id;
  String key;
  String value;

  Role({required this.id, required this.key, required this.value});

  Role copyWith({String? id, String? key, String? value}) =>
      Role(id: id ?? this.id, key: key ?? this.key, value: value ?? this.value);

  factory Role.fromJson(Map<String, dynamic> json) =>
      Role(id: json["id"], key: json["key"], value: json["value"]);

  Map<String, dynamic> toJson() => {"id": id, "key": key, "value": value};
}