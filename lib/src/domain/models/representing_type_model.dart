// To parse this JSON data, do
//
//     final representingTypes = representingTypesFromJson(jsonString);

import 'dart:convert';

List<RepresentingTypes> representingTypesFromJson(String str) => List<RepresentingTypes>.from(json.decode(str).map((x) => RepresentingTypes.fromJson(x)));

String representingTypesToJson(List<RepresentingTypes> data) => json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class RepresentingTypes {
    String? id;
    String? key;
    String? value;

    RepresentingTypes({
        this.id,
        this.key,
        this.value,
    });

    RepresentingTypes copyWith({
        String? id,
        String? key,
        String? value,
    }) => 
        RepresentingTypes(
            id: id ?? this.id,
            key: key ?? this.key,
            value: value ?? this.value,
        );

    factory RepresentingTypes.fromJson(Map<String, dynamic> json) => RepresentingTypes(
        id: json["id"],
        key: json["key"],
        value: json["value"],
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "key": key,
        "value": value,
    };
}
