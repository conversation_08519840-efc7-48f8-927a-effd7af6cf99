// To parse this JSON data, do
//
//     final registeredAgent = registeredAgentFromJson(jsonString);

import 'dart:convert';

RegisteredAgent registeredAgentFromJson(String str) =>
    RegisteredAgent.fromJson(json.decode(str));

String registeredAgentToJson(RegisteredAgent data) =>
    json.encode(data.toJson());

class RegisteredAgent {
  String? id;
  String? firstName;
  String? lastName;
  String? email;
  String? phone;
  String? organizationName;
  String? roleName;
  String? city;
  String? state;
  String? country;
  String? licenseNumber;
  String? zipCode;
  String? recruiterId;
  String? inviteCode;
  dynamic additionalInfo;
  List<String>? uploadedDocuments;

  RegisteredAgent({
    this.id,
    this.firstName,
    this.lastName,
    this.email,
    this.phone,
    this.organizationName,
    this.roleName,
    this.city,
    this.state,
    this.country,
    this.licenseNumber,
    this.zipCode,
    this.recruiterId,
    this.inviteCode,
    this.additionalInfo,
    this.uploadedDocuments,
  });

  RegisteredAgent copyWith({
    String? id,
    String? firstName,
    String? lastName,
    String? email,
    String? phone,
    String? organizationName,
    String? roleName,
    String? city,
    String? state,
    String? country,
    String? licenseNumber,
    String? zipCode,
    String? recruiterId,
    String? inviteCode,
    dynamic additionalInfo,
    List<String>? uploadedDocuments,
  }) => RegisteredAgent(
    id: id ?? this.id,
    firstName: firstName ?? this.firstName,
    lastName: lastName ?? this.lastName,
    email: email ?? this.email,
    phone: phone ?? this.phone,
    organizationName: organizationName ?? this.organizationName,
    roleName: roleName ?? this.roleName,
    city: city ?? this.city,
    state: state ?? this.state,
    country: country ?? this.country,
    licenseNumber: licenseNumber ?? this.licenseNumber,
    zipCode: zipCode ?? this.zipCode,
    recruiterId: recruiterId ?? this.recruiterId,
    inviteCode: inviteCode ?? this.inviteCode,
    additionalInfo: additionalInfo ?? this.additionalInfo,
    uploadedDocuments: uploadedDocuments ?? this.uploadedDocuments,
  );

  factory RegisteredAgent.fromJson(Map<String, dynamic> json) =>
      RegisteredAgent(
        id: json["id"],
        firstName: json["firstName"],
        lastName: json["lastName"],
        email: json["email"],
        phone: json["phone"],
        organizationName: json["organizationName"],
        roleName: json["roleName"],
        city: json["city"],
        state: json["state"],
        country: json["country"],
        licenseNumber: json["licenseNumber"],
        zipCode: json["zipCode"],
        recruiterId: json["recruiterId"],
        inviteCode: json["inviteCode"],
        additionalInfo: json["additionalInfo"],
        uploadedDocuments: json["uploadedDocuments"] == null
            ? []
            : List<String>.from(json["uploadedDocuments"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
    "id": id,
    "firstName": firstName,
    "lastName": lastName,
    "email": email,
    "phone": phone,
    "organizationName": organizationName,
    "roleName": roleName,
    "city": city,
    "state": state,
    "country": country,
    "licenseNumber": licenseNumber,
    "zipCode": zipCode,
    "recruiterId": recruiterId,
    "inviteCode": inviteCode,
    "additionalInfo": additionalInfo,
    "uploadedDocuments": uploadedDocuments == null
        ? []
        : List<dynamic>.from(uploadedDocuments!.map((x) => x)),
  };
}
