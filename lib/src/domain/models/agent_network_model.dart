import 'dart:convert';

AgentNetworkModel agentNetworkModelFromJson(String str) =>
    AgentNetworkModel.fromJson(json.decode(str));

String agentNetworkModelToJson(AgentNetworkModel data) =>
    json.encode(data.toJson());

class AgentNetworkModel {
  UserProfile userProfile;
  List<UserProfile> directRecruits;
  int totalDirectRecruits;
  int currentUserLevel;

  AgentNetworkModel({
    required this.userProfile,
    required this.directRecruits,
    required this.totalDirectRecruits,
    required this.currentUserLevel,
  });

  factory AgentNetworkModel.fromJson(Map<String, dynamic> json) =>
      AgentNetworkModel(
        userProfile: UserProfile.fromJson(json["userProfile"]),
        directRecruits: List<UserProfile>.from(
          json["directRecruits"].map((x) => UserProfile.fromJson(x)),
        ),
        totalDirectRecruits: json["totalDirectRecruits"],
        currentUserLevel: json["currentUserLevel"],
      );

  Map<String, dynamic> toJson() => {
    "userProfile": userProfile.toJson(),
    "directRecruits": List<dynamic>.from(directRecruits.map((x) => x.toJson())),
    "totalDirectRecruits": totalDirectRecruits,
    "currentUserLevel": currentUserLevel,
  };
}

class UserProfile {
  String userId;
  String name;
  String email;
  String role;
  int recruitsCount;
  String phone;
  double totalSalesRevenue;
  double totalCommissionEarned;
  dynamic profileImageUrl;
  bool? depthLimitReached;

  UserProfile({
    required this.userId,
    required this.name,
    required this.email,
    required this.role,
    required this.recruitsCount,
    required this.phone,
    required this.totalSalesRevenue,
    required this.totalCommissionEarned,
    required this.profileImageUrl,
    this.depthLimitReached,
  });

  factory UserProfile.fromJson(Map<String, dynamic> json) => UserProfile(
    userId: json["userId"],
    name: json["name"],
    email: json["email"],
    role: json["role"],
    recruitsCount: json["recruitsCount"] ?? 0,
    phone: json["phone"],
    totalSalesRevenue: json["totalSalesRevenue"] ?? 0,
    totalCommissionEarned: json["totalCommissionEarned"] ?? 0,
    profileImageUrl: json["profileImageUrl"] ?? "",
    depthLimitReached: json["depthLimitReached"] ?? false,
  );

  Map<String, dynamic> toJson() => {
    "userId": userId,
    "name": name,
    "email": email,
    "role": role,
    "recruitsCount": recruitsCount,
    "phone": phone,
    "totalSalesRevenue": totalSalesRevenue,
    "totalCommissionEarned": totalCommissionEarned,
    "profileImageUrl": profileImageUrl,
    "depthLimitReached": depthLimitReached,
  };
}
