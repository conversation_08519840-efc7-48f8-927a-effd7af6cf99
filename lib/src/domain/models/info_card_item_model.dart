import 'dart:convert';

InfoCardItemModel infoCardItemModelFromJson(String str) =>
    InfoCardItemModel.fromJson(json.decode(str));

String infoCardItemModelToJson(InfoCardItemModel data) =>
    json.encode(data.toJson());

class InfoCardItemModel {
  Data data;
  String status;
  String message;

  InfoCardItemModel({
    required this.data,
    required this.status,
    required this.message,
  });

  factory InfoCardItemModel.fromJson(Map<String, dynamic> json) =>
      InfoCardItemModel(
        data: Data.fromJson(json["data"]),
        status: json["status"],
        message: json["message"],
      );

  Map<String, dynamic> toJson() => {
    "data": data.toJson(),
    "status": status,
    "message": message,
  };
}

class Data {
  String key;
  String type;
  String? year; // Made nullable to handle missing or null year
  double value;

  Data({
    required this.key,
    required this.type,
    this.year, // No longer required, can be null
    required this.value,
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
    key: json["key"],
    type: json["type"],
    year: json["year"], // Will be null if year is missing or null in JSON
    value: json["value"],
  );

  Map<String, dynamic> toJson() => {
    "key": key,
    "type": type,
    if (year != null) "year": year, // Only include year if it's not null
    "value": value,
  };
}
