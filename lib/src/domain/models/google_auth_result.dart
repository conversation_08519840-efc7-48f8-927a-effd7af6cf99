class GoogleAuthResult {
  final bool success;
  final String? error;
  final String? idToken;
  final String? accessToken;
  final String? email;
  final String? displayName;
  final String? photoUrl;
  final String? uid;

  GoogleAuthResult({
    required this.success,
    this.error,
    this.idToken,
    this.accessToken,
    this.email,
    this.displayName,
    this.photoUrl,
    this.uid,
  });

  factory GoogleAuthResult.success({
    required String idToken,
    String? accessToken,
    String? email,
    String? displayName,
    String? photoUrl,
    String? uid,
  }) {
    return GoogleAuthResult(
      success: true,
      idToken: idToken,
      accessToken: accessToken,
      email: email,
      displayName: displayName,
      photoUrl: photoUrl,
      uid: uid,
    );
  }

  factory GoogleAuthResult.failure(String error) {
    return GoogleAuthResult(
      success: false,
      error: error,
    );
  }

  @override
  String toString() {
    return 'GoogleAuthResult(success: $success, error: $error, email: $email)';
  }
}
