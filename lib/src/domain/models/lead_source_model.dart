// To parse this JSON data, do
//
//     final leadSource = leadSourceFromJson(jsonString);

import 'dart:convert';

List<LeadSource> leadSourceFromJson(String str) => List<LeadSource>.from(json.decode(str).map((x) => LeadSource.fromJson(x)));

String leadSourceToJson(List<LeadSource> data) => json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class LeadSource {
    String? id;
    String? key;
    String? value;

    LeadSource({
        this.id,
        this.key,
        this.value,
    });

    LeadSource copyWith({
        String? id,
        String? key,
        String? value,
    }) => 
        LeadSource(
            id: id ?? this.id,
            key: key ?? this.key,
            value: value ?? this.value,
        );

    factory LeadSource.fromJson(Map<String, dynamic> json) => LeadSource(
        id: json["id"],
        key: json["key"],
        value: json["value"],
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "key": key,
        "value": value,
    };
}
