import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';
import 'package:neorevv/src/domain/models/lead_source_model.dart';
import 'package:neorevv/src/domain/models/representing_type_model.dart';
import 'package:neorevv/src/domain/models/transaction_type_model.dart';

import '../../../core/services/exceptions.dart';
import '../../../domain/models/sales.dart';
import '../../../domain/models/sales_details.dart';
import '../../../domain/repository/sales_details_repository.dart';

part 'sales_details_state.dart';

class SalesDetailsCubit extends Cubit<SalesDetailsState> {
  SalesDetailsCubit(this._salesDetailsRepository)
    : super(SalesDetailsInitial());
  final SalesDetailsRepository _salesDetailsRepository;

  // only used to restore values in the closing document screen for cancel button action
  SalesClosingDocDetailsApi? salesFormDetailsApi;
  List<TransactionType?> transactionTypeData = [];
  List<RepresentingTypes?> representingTypesData = [];
  List<LeadSource?> leadSourceData = [];

  Future<void> fetchSalesDetails(Map<String, dynamic> payload) async {
    emit(SalesDetailsLoading());

    try {
      final salesDetails = await _salesDetailsRepository.getSalesDetails(
        payload,
      );
      emit(SalesDetailsLoaded(salesDetailsApi: salesDetails));
    } on ApiException catch (e) {
      emit(
        SalesDetailsError(
          message: e.message.toString(),
          statusCode: e.statusCode,
        ),
      );
    } catch (e) {
      emit(
        SalesDetailsError(
          message: 'An unexpected error occurred: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> fetchSalesClosingDocumentDetails(
    String salesID,
    String requestingUserId,
  ) async {
    emit(SalesDetailsLoading());

    try {
      final salesDetails = await _salesDetailsRepository
          .getSalesClosingDocumentDetails(salesID, requestingUserId);
      salesFormDetailsApi = salesDetails;
      emit(
        SalesDetailsClosingDocumentLoaded(
          salesClosingDocDetailsApi: salesDetails,
        ),
      );
    } on ApiException catch (e) {
      emit(
        SalesDetailsError(
          message: e.message.toString(),
          statusCode: e.statusCode,
        ),
      );
    } catch (e) {
      emit(
        SalesDetailsError(
          message: 'An unexpected error occurred: ${e.toString()}',
        ),
      );
    }
  }

  /// Upload agent file
  Future<void> uploadDocFile(Map<String, dynamic> requestBody) async {
    emit(SalesDetailsLoading());
    try {
      final response = await _salesDetailsRepository
          .uploadSalesClosingDocumentFile(requestBody);
      emit(
        SalesClosingDocumentFileUploaded(
          message: response['message'],
          salesId:
              response['data']['fileId']
                  as String, // Get salesId from request payload
        ),
      );
    } on ApiException catch (e) {
      emit(
        SalesDetailsError(
          message: e.message.toString(),
          statusCode: e.statusCode,
        ),
      );
    } catch (e) {
      emit(
        SalesDetailsError(
          message: 'An unexpected error occurred: ${e.toString()}',
        ),
      );
    }
  }

  Future<dynamic> editClosingDocument(
    Map<String, dynamic> requestBody,
    String salesID,
  ) async {
    emit(SalesDetailsLoading());
    try {
      final response = await _salesDetailsRepository.editSalesClosingDocument(
        requestBody,
        salesID,
      );
      final message =
          response is Map<String, dynamic> && response['message'] != null
          ? response['message'].toString()
          : 'Sales closing document edited successfully';

      emit(SalesClosingDocumentEdited(message: message));
    } on ApiException catch (e) {
      emit(
        SalesDetailsError(
          message: e.message.toString(),
          statusCode: e.statusCode,
        ),
      );
    } catch (e) {
      emit(
        SalesDetailsError(
          message: 'An unexpected error occurred: ${e.toString()}',
        ),
      );
    }
  }

  Future<dynamic> updateSalesClosingDocUploadStatus(
    String salesId,
    Map<String, dynamic> requestBody,
  ) async {
    emit(SalesDetailsLoading());
    try {
      final response = await _salesDetailsRepository
          .updateSalesClosingDocUploadStatus(salesId, requestBody);
      final message =
          response is Map<String, dynamic> && response['message'] != null
          ? response['message'].toString()
          : 'Sales closing document edited successfully';

      emit(SalesClosingDocumentUploadStatusUpdated(message: message));
    } on ApiException catch (e) {
      emit(
        SalesDetailsError(
          message: e.message.toString(),
          statusCode: e.statusCode,
        ),
      );
    } catch (e) {
      emit(
        SalesDetailsError(
          message: 'An unexpected error occurred: ${e.toString()}',
        ),
      );
    }
  }

  Future<dynamic> updateSalesClosingDocUploadCommission(String salesId) async {
    emit(SalesDetailsLoading());
    try {
      final response = await _salesDetailsRepository
          .updateSalesClosingDocCommissionUpdate(salesId);
      final message =
          response is Map<String, dynamic> && response['message'] != null
          ? response['message'].toString()
          : 'Sales closing document edited successfully';

      emit(SalesClosingDocumentUploadCommissionUpdated(message: message));
    } on ApiException catch (e) {
      emit(
        SalesDetailsError(
          message: e.message.toString(),
          statusCode: e.statusCode,
        ),
      );
    } catch (e) {
      emit(
        SalesDetailsError(
          message: 'An unexpected error occurred: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> fetchTransactionTypes() async {
    emit(SalesDetailsLoading());

    try {
      final transactionTypeList = await _salesDetailsRepository
          .getTransactionTypes();
      transactionTypeData = transactionTypeList;
      emit(
        SalesDetailsTransactionTypesLoaded(
          transactionTypeList: transactionTypeList,
        ),
      );
    } on ApiException catch (e) {
      emit(
        SalesDetailsError(
          message: e.message.toString(),
          statusCode: e.statusCode,
        ),
      );
    } catch (e) {
      emit(
        SalesDetailsError(
          message: 'An unexpected error occurred: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> fetchRepresentingTypes() async {
    emit(SalesDetailsLoading());

    try {
      final representingTypeList = await _salesDetailsRepository
          .getRepresentingTypes();
      representingTypesData = representingTypeList;
      emit(
        SalesDetailsRepresentingLoaded(representingTypes: representingTypeList),
      );
    } on ApiException catch (e) {
      emit(
        SalesDetailsError(
          message: e.message.toString(),
          statusCode: e.statusCode,
        ),
      );
    } catch (e) {
      emit(
        SalesDetailsError(
          message: 'An unexpected error occurred: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> fetchLeadSourceTypes() async {
    emit(SalesDetailsLoading());

    try {
      final leadSourceList = await _salesDetailsRepository.getLeadSourceTypes();
      leadSourceData = leadSourceList;
      emit(SalesDetailsLeadSourceLoaded(leadSourceList: leadSourceList));
    } on ApiException catch (e) {
      emit(
        SalesDetailsError(
          message: e.message.toString(),
          statusCode: e.statusCode,
        ),
      );
    } catch (e) {
      emit(
        SalesDetailsError(
          message: 'An unexpected error occurred: ${e.toString()}',
        ),
      );
    }
  }

  void updateTransactionType(TransactionType? type) {
    final currentState = state;
    if (currentState is SalesDetailsFormState) {
      emit(currentState.copyWith(selectedTransactionType: type));
    } else {
      emit(SalesDetailsFormState(selectedTransactionType: type));
    }
  }

  void updateRepresentingType(RepresentingTypes? type) {
    final currentState = state;
    if (currentState is SalesDetailsFormState) {
      emit(currentState.copyWith(selectedRepresentingType: type));
    } else {
      emit(SalesDetailsFormState(selectedRepresentingType: type));
    }
  }

  void updateLeadSource(LeadSource? leadSource) {
    final currentState = state;
    if (currentState is SalesDetailsFormState) {
      emit(currentState.copyWith(selectedLeadSource: leadSource));
    } else {
      emit(SalesDetailsFormState(selectedLeadSource: leadSource));
    }
  }

  void updateClosingDocFileName(String? fileName) {
    final currentState = state;
    if (currentState is SalesDetailsFormState) {
      emit(currentState.copyWith(closingDocFileName: fileName));
    } else {
      emit(SalesDetailsFormState(closingDocFileName: fileName));
    }
  }

  void updateClosingDocFileUrl(String? fileUrl) {
    final currentState = state;
    if (currentState is SalesDetailsFormState) {
      emit(currentState.copyWith(closingDocFileUrl: fileUrl));
    } else {
      emit(SalesDetailsFormState(closingDocFileUrl: fileUrl));
    }
  }
}
