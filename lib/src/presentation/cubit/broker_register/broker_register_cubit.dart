import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';
import '../../../core/config/app_strings.dart';
import '../../../core/services/exceptions.dart';
import '../../../domain/repository/broker_register_repository.dart';

part 'broker_register_state.dart';

class BrokerRegisterCubit extends Cubit<BrokerRegisterState> {
  BrokerRegisterCubit(this._brokerRegisterRepository)
    : super(BrokerRegisterInitial());
  final BrokerRegisterRepository _brokerRegisterRepository;

  Future<void> registerBrokerWithFiles({
    required Map<String, dynamic> brokerPayload,
    required List<FileUploadData> files,
  }) async {
    emit(BrokerRegisterLoading(true));
    try {
      final userId = await _brokerRegisterRepository.registerBroker(
        brokerPayload,
      );
      if (userId != null && userId.isNotEmpty) {
        if (files.isNotEmpty) {
          await _brokerRegisterRepository.registerBrokerWithFiles(
            brokerPayload: brokerPayload,
            files: files,
            userId: userId,
          );
          emit(BrokerRegisterSuccess(userId: userId));
        } else {
          emit(BrokerRegisterSuccessWithoutFiles(userId: userId));
        }
      } else {
        emit(BrokerRegisterError(error: failedToRegisterBroker));
      }
    } on ApiException catch (e) {
      emit(BrokerRegisterError(error: e.message));
    } catch (e) {
      emit(BrokerRegisterError(error: '$unexpectedError: ${e.toString()}'));
    }
  }

  /// Invokes the status update API when:
  /// - A brokerage is invited without file selection
  /// - A brokerage is invited with file selection (after successful upload)

  Future<void> handleBrokerageStatusUpdate(
    String userId,
    bool uploadStatus,
  ) async {
    emit(BrokerRegisterLoading(true));
    try {
      Map<String, dynamic> requestBody = {
        "status": uploadStatus,
        "userActionType": "REGISTER",
      };
      final success = await _brokerRegisterRepository
          .handleBrokerageStatusUpdate(requestBody, userId);
      if (success) {
        emit(BrokerRegisterStatusUpdated());
      } else {
        emit(
          BrokerRegisterStatusUpdateFailed(
            message: 'Failed to update brokerage registration status',
          ),
        );
      }
    } on ApiException catch (e) {
      emit(BrokerRegisterStatusUpdateFailed(message: e.message));
    } catch (e) {
      emit(
        BrokerRegisterStatusUpdateFailed(
          message: 'An unexpected error occurred: ${e.toString()}',
        ),
      );
    }
  }
}
