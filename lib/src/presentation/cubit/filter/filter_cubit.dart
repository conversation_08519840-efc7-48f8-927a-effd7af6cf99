import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';

import '../../../core/services/exceptions.dart';
import '../../../domain/models/filter/table_filter.dart';
import '../../../domain/repository/filter_repository.dart';

part 'filter_state.dart';

class FilterCubit extends Cubit<FilterState> {
  final FilterRepository _filterRepository;
  FilterCubit(this._filterRepository) : super(FilterInitial());

  Future<void> getProperyTypeFilterOptions() async {
    emit(FilterLoading());
    try {
      final filterOptions = await _filterRepository
          .getProperyTypeFilterOptions();
      emit(FilterLoaded(filterOptions: filterOptions));
    } on ApiException catch (e) {
      emit(FilterError(message: e.message, statusCode: e.statusCode));
    } catch (e) {
      emit(
        FilterError(message: 'An unexpected error occurred: ${e.toString()}'),
      );
    }
  }

  Future<void> getBrokerageFilterOptions() async {
    emit(FilterLoading());
    try {
      final filterOptions = await _filterRepository.getBrokerageFilterOptions();
      emit(FilterLoaded(filterOptions: filterOptions));
    } on ApiException catch (e) {
      emit(FilterError(message: e.message, statusCode: e.statusCode));
    } catch (e) {
      emit(
        FilterError(message: 'An unexpected error occurred: ${e.toString()}'),
      );
    }
  }

  Future<void> getAgentFilterOptions({
    String? userId,
    String? selectedValueParam,
  }) async {
    emit(FilterLoading());
    try {
      final filterOptions = await _filterRepository.getAgentFilterOptions(
        userId,
        selectedValueParam,
      );
      emit(FilterLoaded(filterOptions: filterOptions));
    } on ApiException catch (e) {
      emit(FilterError(message: e.message, statusCode: e.statusCode));
    } catch (e) {
      emit(
        FilterError(message: 'An unexpected error occurred: ${e.toString()}'),
      );
    }
  }

  Future<void> getUserStatusFilterOptions() async {
    emit(FilterLoading());
    try {
      final filterOptions = await _filterRepository
          .getUserStatusFilterOptions();
      emit(FilterLoaded(filterOptions: filterOptions));
    } on ApiException catch (e) {
      emit(FilterError(message: e.message, statusCode: e.statusCode));
    } catch (e) {
      emit(
        FilterError(message: 'An unexpected error occurred: ${e.toString()}'),
      );
    }
  }

  Future<void> getCountiresFilterOptions() async {
    emit(FilterLoading());
    try {
      final filterOptions = await _filterRepository.getCountiresFilterOptions();
      emit(FilterLoaded(filterOptions: filterOptions));
    } on ApiException catch (e) {
      emit(FilterError(message: e.message, statusCode: e.statusCode));
    } catch (e) {
      emit(
        FilterError(message: 'An unexpected error occurred: ${e.toString()}'),
      );
    }
  }

  Future<void> getStatesFilterOptions({String? selectedCountry}) async {
    emit(FilterLoading());
    try {
      final filterOptions = await _filterRepository.getStatesFilterOptions(
        selectedCountry,
      );
      emit(FilterLoaded(filterOptions: filterOptions));
    } on ApiException catch (e) {
      emit(FilterError(message: e.message, statusCode: e.statusCode));
    } catch (e) {
      emit(
        FilterError(message: 'An unexpected error occurred: ${e.toString()}'),
      );
    }
  }

  Future<void> getCitiesFilterOptions({
    String? selectedState,
    String? searchString,
  }) async {
    if (selectedState == null && searchString == null) {
      emit(FilterLoaded(filterOptions: []));
      return;
    }
    emit(FilterLoading());

    try {
      final filterOptions = await _filterRepository.getCitiesFilterOptions(
        selectedState,
        searchString,
      );
      emit(FilterLoaded(filterOptions: filterOptions));
    } on ApiException catch (e) {
      emit(FilterError(message: e.message, statusCode: e.statusCode));
    } catch (e) {
      emit(
        FilterError(message: 'An unexpected error occurred: ${e.toString()}'),
      );
    }
  }
}
