import 'dart:core';

import 'package:hydrated_bloc/hydrated_bloc.dart';
import '../../../core/services/exceptions.dart';
import '../../../domain/models/top_performers.dart';
import '../../../domain/repository/top_performers_repository.dart';

part 'top_performers_state.dart';

class TopPerformersCubit extends Cubit<TopPerformersState> {
  TopPerformersCubit(this._topPerformersRepository)
    : super(TopPerformersInitial());
  final TopPerformersRepository _topPerformersRepository;

  Future<void> getBrokerageTopPerformers(String date, {bool preserveAgentData = false}) async {
    if (!preserveAgentData) {
      emit(TopPerformersLoading());
    }
    try {
      final topPerformers = await _topPerformersRepository.getBrokerageTopPerformers(date);
      // Preserve existing agent data if requested
      final currentState = state;
      final existingAgentData = preserveAgentData && currentState is TopPerformersLoaded
          ? currentState.agentTopPerformers
          : null;
      emit(TopPerformersLoaded(
        brokerageTopPerformers: topPerformers,
        agentTopPerformers: existingAgentData,
      ));
    } on ApiException catch (e) {
      emit(TopPerformersError(message: e.message, statusCode: e.statusCode));
    } catch (e) {
      emit(
        TopPerformersError(
          message: 'An unexpected error occurred: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> getAgentTopPerformers(String date, {bool preserveBrokerageData = false}) async {
    if (!preserveBrokerageData) {
      emit(TopPerformersLoading());
    }
    try {
      final topPerformers = await _topPerformersRepository.getAgentTopPerformers(date);
      // Preserve existing brokerage data if requested
      final currentState = state;
      final existingBrokerageData = preserveBrokerageData && currentState is TopPerformersLoaded
          ? currentState.brokerageTopPerformers
          : null;
      emit(TopPerformersLoaded(
        agentTopPerformers: topPerformers,
        brokerageTopPerformers: existingBrokerageData,
      ));
    } on ApiException catch (e) {
      emit(TopPerformersError(message: e.message, statusCode: e.statusCode));
    } catch (e) {
      emit(
        TopPerformersError(
          message: 'An unexpected error occurred: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> getBothTopPerformers(String date) async {
    emit(TopPerformersLoading());
    try {
      // Fetch both broker and agent data
      final brokerageTopPerformers = await _topPerformersRepository.getBrokerageTopPerformers(date);
      final agentTopPerformers = await _topPerformersRepository.getAgentTopPerformers(date);

      emit(TopPerformersLoaded(
        brokerageTopPerformers: brokerageTopPerformers,
        agentTopPerformers: agentTopPerformers,
      ));
    } on ApiException catch (e) {
      emit(TopPerformersError(message: e.message, statusCode: e.statusCode));
    } catch (e) {
      emit(
        TopPerformersError(
          message: 'An unexpected error occurred: ${e.toString()}',
        ),
      );
    }
  }

}