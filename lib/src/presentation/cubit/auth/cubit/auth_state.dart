part of 'auth_cubit.dart';

@immutable
sealed class AuthState {}

final class AuthInitial extends AuthState {}

final class AuthLoading extends AuthState {
  final bool isLoading;
  AuthLoading(this.isLoading);
  @override
  List<Object> get props => [isLoading];
}

final class AuthGoogleLoading extends AuthState {
  final bool isLoading;
  AuthGoogleLoading(this.isLoading);
  @override
  List<Object> get props => [isLoading];
}

final class AuthAppleLoading extends AuthState {
  final bool isLoading;
  AuthAppleLoading(this.isLoading);
  @override
  List<Object> get props => [isLoading];
}

final class AuthSuccess extends AuthState {
  AuthSuccess();
}

final class AuthError extends AuthState {
  final bool invalidCredentials;
  final String error;
  AuthError({required this.error, required this.invalidCredentials});
}

final class AuthNavigateToLogin extends AuthState {
  AuthNavigateToLogin();
}

final class AuthGoogleError extends AuthState {
  final bool invalidCredentials;
  final String error;
  final String? idToken;
  final bool userNotFound;
  AuthGoogleError({
    required this.error,
    required this.invalidCredentials,
    required this.idToken,
    required this.userNotFound,
  });
}

final class AuthAppleError extends AuthState {
  final String error;
  final bool invalidCredentials;
  final String? idToken;
  final bool userNotFound;

  AuthAppleError({
    required this.error,
    required this.invalidCredentials,
    required this.idToken,
    required this.userNotFound,
  });
}

final class CreatePasswordSuccess extends AuthState {
  CreatePasswordSuccess();
}

final class CreatePasswordFailure extends AuthState {
  final String error;
  CreatePasswordFailure({required this.error});
}

final class AuthGoogleSuccess extends AuthState {
  final String idToken;
  final String? email;
  final String? displayName;
  final String? photoUrl;
  final String? uid;

  AuthGoogleSuccess({
    required this.idToken,
    this.email,
    this.displayName,
    this.photoUrl,
    this.uid,
  });

  List<Object?> get props => [idToken, email, displayName, photoUrl, uid];
}

final class AuthAppleSuccess extends AuthState {
  final String idToken;
  final String? email;
  final String? displayName;
  final String? photoUrl;
  final String? uid;

  AuthAppleSuccess({
    required this.idToken,
    this.email,
    this.displayName,
    this.photoUrl,
    this.uid,
  });

  List<Object?> get props => [idToken, email, displayName, photoUrl, uid];
}

final class AuthLogout extends AuthState {
  AuthLogout();
}

class AuthLogoutError extends AuthState {
  final String error;
  AuthLogoutError({required this.error});
}

// Forgot Password
final class EmailVerificationSuccess extends AuthState {
  final String message;
  EmailVerificationSuccess({required this.message});
}

final class EmailVerificationFailure extends AuthState {
  final String error;
  EmailVerificationFailure({required this.error});
}

final class EmailVerificationLoading extends AuthState {
  EmailVerificationLoading();
  @override
  List<Object> get props => [];
}

final class CreateNewPasswordSuccess extends AuthState {
  final String message;
  CreateNewPasswordSuccess({required this.message});
}

final class CreateNewPasswordFailure extends AuthState {
  final String error;
  CreateNewPasswordFailure({required this.error});
}

final class CreateNewPasswordLoading extends AuthState {
  CreateNewPasswordLoading();
  @override
  List<Object> get props => [];
}

// Reset Password
final class ResetPasswordSuccess extends AuthState {
  final String message;
  ResetPasswordSuccess({required this.message});
}

final class ResetPasswordFailure extends AuthState {
  final String error;
  ResetPasswordFailure({required this.error});
}

final class ResetPasswordLoading extends AuthState {
  ResetPasswordLoading();
  @override
  List<Object> get props => [];
}

//TokenValidation
final class TokenValidationLoading extends AuthState {
  TokenValidationLoading();
  @override
  List<Object> get props => [];
}

final class TokenValidationSuccess extends AuthState {
  final String message;
  TokenValidationSuccess({required this.message});
}

final class TokenValidationFailure extends AuthState {
  final String error;
  TokenValidationFailure({required this.error});
}
