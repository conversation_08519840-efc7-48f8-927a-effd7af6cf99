part of 'agent_network_cubit.dart';

@immutable
sealed class AgentNetworkState {}

final class AgentNetworkInitial extends AgentNetworkState {}

final class AgentNetworkLoading extends AgentNetworkState {}

final class AgentNetworkSuccess extends AgentNetworkState {
  final Agent agent;
  final Broker broker;
  final List<Agent> hierarchyPath;
  final List<Agent> upstreamUsers;

  AgentNetworkSuccess({
    required this.agent,
    required this.broker,
    required this.hierarchyPath,
    this.upstreamUsers = const [],
  });
}

final class AgentUpstreamSuccess extends AgentNetworkState {
  final List<Agent> upstreamUsers;

  AgentUpstreamSuccess({required this.upstreamUsers});
}

final class AgentNetworkError extends AgentNetworkState {
  final String message;

  AgentNetworkError({this.message = 'Unknown error occurred'});
}
