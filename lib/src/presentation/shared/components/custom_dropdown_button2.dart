import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import '../../../core/theme/app_fonts.dart';
import '../../../core/theme/app_theme.dart';
import '../../../domain/models/filter/table_filter.dart';

class CustomDropdownButton2 extends StatefulWidget {
  final String hint;
  final List<TableFilter> items;
  final String? selectedValue;
  final ValueChanged<String?> onChanged;
  final String? Function(String?)? validator;
  final bool enableSearch;
  final double? width;
  final double? height;
  final bool isRequired;
  final bool useApiSearch;
  final Function(String)? onSearchChanged;

  const CustomDropdownButton2({
    Key? key,
    required this.hint,
    required this.items,
    this.selectedValue,
    required this.onChanged,
    this.validator,
    this.enableSearch = true,
    this.width,
    this.height = 50,
    this.isRequired = false,
    this.useApiSearch = false, 
    this.onSearchChanged,
  }) : super(key: key);

  @override
  State<CustomDropdownButton2> createState() => _CustomDropdownButton2State();
}

class _CustomDropdownButton2State extends State<CustomDropdownButton2> {
  final TextEditingController _searchController = TextEditingController();
  late ValueNotifier<String?> _valueNotifier;

  @override
  void initState() {
    super.initState();
    _valueNotifier = ValueNotifier(_getValidatedValue(widget.selectedValue));
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _valueNotifier.value = _getValidatedValue(widget.selectedValue);
    });
  }

  @override
  void didUpdateWidget(CustomDropdownButton2 oldWidget) {
    super.didUpdateWidget(oldWidget);
    final newValue = _getValidatedValue(widget.selectedValue);

    if (oldWidget.selectedValue != widget.selectedValue ||
        oldWidget.items.length != widget.items.length ||
        _valueNotifier.value != newValue) {
      if (_valueNotifier.value != newValue) {
        _valueNotifier.value = newValue;
      }
    }
  }

  String? _getValidatedValue(String? value) {
    if (value == null || widget.items.isEmpty) return null;
    final exists = widget.items.any((item) => item.id == value);
    return exists ? value : null;
  }

  @override
  void dispose() {
    _searchController.dispose();
    _valueNotifier.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FormField<String>(
      validator: widget.validator,
      builder: (FormFieldState<String> field) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: widget.width,
              height: widget.height,
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                border: Border.all(
                  color: field.hasError ? Colors.red : AppTheme.comboBoxBorder,
                  width: 1.0,
                ),
                borderRadius: BorderRadius.circular(25),
              ),
              child: widget.items.isEmpty
                  ? _buildEmptyDropdown()
                  : _buildDropdown(field),
            ),
            if (field.hasError)
              Padding(
                padding: const EdgeInsets.only(top: 5, left: 12),
                child: Text(
                  field.errorText!,
                  style: AppFonts.regularTextStyle(12, color: Colors.red),
                ),
              ),
          ],
        );
      },
    );
  }

  Widget _buildEmptyDropdown() {
    return Container(
      alignment: Alignment.centerLeft,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            widget.hint,
            style: AppFonts.regularTextStyle(14, color: Colors.grey[600]!),
          ),
          const Icon(Icons.keyboard_arrow_down, size: 20, color: Colors.grey),
        ],
      ),
    );
  }

  Widget _buildDropdown(FormFieldState<String> field) {
    return DropdownButton2<String>(
      isExpanded: true,
      hint: Text(
        widget.hint,
        style: AppFonts.regularTextStyle(14, color: Colors.grey[600]!),
      ),
      // Custom builder to show selected item in black in the text field
      selectedItemBuilder: (BuildContext context) {
        return widget.items.map((item) {
          return Align(
            alignment: Alignment.centerLeft,
            child: Padding(
              padding: const EdgeInsets.only(
                left: 0,
                right: 16,
                top: 12,
                bottom: 12,
              ),
              child: Text(
                item.value,
                style: AppFonts.regularTextStyle(14, color: AppTheme.black),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          );
        }).toList();
      },
      // Dropdown menu items - selected item shown in primary blue
      items: widget.items
          .map(
            (item) => DropdownItem<String>(
              value: item.id,
              child: Padding(
                padding: const EdgeInsets.only(
                  left: 0,
                  right: 16,
                  top: 12,
                  bottom: 12,
                ),
                child: Text(
                  item.value,
                  style: AppFonts.regularTextStyle(
                    14,
                    color: _valueNotifier.value == item.id
                        ? AppTheme.primaryBlueColor
                        : AppTheme.black,
                  ),
                ),
              ),
            ),
          )
          .toList(),
      valueListenable: _valueNotifier,
      onChanged: (value) {
        final validatedValue = _getValidatedValue(value);
        _valueNotifier.value = validatedValue;
        field.didChange(validatedValue);
        widget.onChanged(validatedValue);
      },
      underline: const SizedBox(),
      onMenuStateChange: (isOpen) {
        if (!isOpen) {
          _searchController.clear();
        }
      },
      dropdownSearchData: widget.enableSearch && widget.items.length > 5
          ? DropdownSearchData(
              searchController: _searchController,
              searchBarWidget: Padding(
                padding: const EdgeInsets.all(8),
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'Search...',
                    hintStyle: AppFonts.regularTextStyle(
                      14,
                      color: Colors.grey[600]!,
                    ),
                    isDense: true,
                    border: const OutlineInputBorder(),
                    contentPadding: const EdgeInsets.symmetric(
                      vertical: 10,
                      horizontal: 8,
                    ),
                  ),
                  onChanged: (value) {
                    widget.onSearchChanged?.call(value);
                  },
                ),
              ),
              searchBarWidgetHeight: 60,
              noResultsWidget: Padding(
                padding: const EdgeInsets.all(8),
                child: Text(
                  'No Item Found!',
                  style: AppFonts.regularTextStyle(14, color: AppTheme.black),
                ),
              ),
              searchMatchFn: (item, searchValue) {
                if (widget.useApiSearch) return true; 
                final label = widget.items
                    .firstWhere((element) => element.id == item.value)
                    .value;
                return label.toLowerCase().contains(searchValue.toLowerCase());
              },
            )
          : null,
      dropdownStyleData: DropdownStyleData(
        maxHeight: 300,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: AppTheme.comboBoxBorder),
        ),
      ),
      buttonStyleData: const ButtonStyleData(
        padding: EdgeInsets.zero,
        height: double.infinity,
      ),
      iconStyleData: const IconStyleData(
        icon: Icon(Icons.keyboard_arrow_down),
        iconSize: 20,
      ),
    );
  }
}
