import 'package:flutter/material.dart';
import '../../../../core/theme/app_fonts.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../domain/models/filter/table_filter.dart';

class CustomCityDropdown extends StatefulWidget {
  final String hint;
  final String? selectedId;
  final List<TableFilter> optionsList;
  final Function(String?) onChanged;
  final Future<void> Function(String)? onSearchChanged;
  final bool showCross;

  const CustomCityDropdown({
    super.key,
    required this.hint,
    required this.selectedId,
    required this.optionsList,
    required this.onChanged,
    this.onSearchChanged,
    this.showCross = false,
  });

  @override
  State<CustomCityDropdown> createState() => _CustomCityDropdownState();
}

class _CustomCityDropdownState extends State<CustomCityDropdown> {
  final TextEditingController _searchController = TextEditingController();
  final LayerLink _layerLink = LayerLink();
  OverlayEntry? _overlayEntry;
  bool _isOpen = false;
  final FocusNode _focusNode = FocusNode();

  @override
  void didUpdateWidget(CustomCityDropdown oldWidget) {
    super.didUpdateWidget(oldWidget);
    // If dropdown is open and options changed, rebuild the overlay
    if (_isOpen && oldWidget.optionsList != widget.optionsList) {
      // Ensure overlay stays open and updates with new data
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _updateOverlay();
      });
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    _focusNode.dispose();
    _removeOverlay();
    super.dispose();
  }

  void _updateOverlay() {
    if (_overlayEntry != null) {
      _overlayEntry!.markNeedsBuild();
    }
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  void _toggleDropdown() {
    if (_isOpen) {
      _closeDropdown();
    } else {
      _openDropdown();
    }
  }

  void _closeDropdown() {
    _removeOverlay();
    setState(() {
      _isOpen = false;
    });
  }

  void _openDropdown() {
    _overlayEntry = _createOverlayEntry();
    Overlay.of(context).insert(_overlayEntry!);
    setState(() {
      _isOpen = true;
    });
    _searchController.clear();
  }

  OverlayEntry _createOverlayEntry() {
    RenderBox renderBox = context.findRenderObject() as RenderBox;
    var size = renderBox.size;

    return OverlayEntry(
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          return GestureDetector(
            onTap: _closeDropdown,
            behavior: HitTestBehavior.translucent,
            child: Stack(
              children: [
                Positioned(
                  width: size.width,
                  child: CompositedTransformFollower(
                    link: _layerLink,
                    showWhenUnlinked: false,
                    offset: Offset(0.0, size.height + 5.0),
                    child: GestureDetector(
                      onTap: () {}, // Prevent closing when tapping on dropdown content
                      child: Material(
                        elevation: 4.0,
                        borderRadius: BorderRadius.circular(8),
                        child: _buildDropdownList(),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildDropdownList() {
    final displayItems = widget.optionsList.isEmpty
        ? [TableFilter(key: 'Select', id: 'Select', value: 'No data found')]
        : widget.optionsList;

    return Container(
      constraints: const BoxConstraints(maxHeight: 300),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Search field
          Padding(
            padding: const EdgeInsets.all(8),
            child: TextField(
              controller: _searchController,
              autofocus: true,
              decoration: InputDecoration(
                hintText: 'Search...',
                hintStyle: AppFonts.regularTextStyle(14, color: AppTheme.black),
                isDense: true,
                border: const OutlineInputBorder(),
                contentPadding: const EdgeInsets.symmetric(
                  vertical: 10,
                  horizontal: 8,
                ),
              ),
              onChanged: (searchText) async {
                if (widget.onSearchChanged != null) {
                  await widget.onSearchChanged!(searchText);
                }
              },
            ),
          ),
          // List of items
          Flexible(
            child: displayItems.isEmpty
                ? Padding(
                    padding: const EdgeInsets.all(8),
                    child: Text(
                      'No Item Found!',
                      style: AppFonts.regularTextStyle(14, color: AppTheme.black),
                    ),
                  )
                : ListView.builder(
                    shrinkWrap: true,
                    padding: EdgeInsets.zero,
                    itemCount: displayItems.length,
                    itemBuilder: (context, index) {
                      final item = displayItems[index];
                      final isSelected = widget.selectedId == item.id.toString();
                      
                      return InkWell(
                        onTap: () {
                          widget.onChanged(item.id.toString());
                          _closeDropdown();
                        },
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 12,
                          ),
                          child: Text(
                            item.value.toString(),
                            style: AppFonts.regularTextStyle(
                              14,
                              color: isSelected
                                  ? AppTheme.primaryBlueColor
                                  : AppTheme.black,
                            ),
                          ),
                        ),
                      );
                    },
                  ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final bool isSelected = widget.selectedId != null &&
        widget.selectedId!.isNotEmpty &&
        widget.selectedId != 'Select';
    final borderColor = isSelected
        ? AppTheme.selectedComboBoxBorder
        : AppTheme.comboBoxBorder;

    final displayItems = widget.optionsList.isEmpty
        ? [TableFilter(key: 'Select', id: 'Select', value: 'No data found')]
        : widget.optionsList;

    final selectedItem = widget.selectedId != null
        ? displayItems.firstWhere(
            (item) => item.id.toString() == widget.selectedId,
            orElse: () => TableFilter(key: '', id: '', value: widget.hint),
          )
        : null;

    return CompositedTransformTarget(
      link: _layerLink,
      child: Container(
        height: 40,
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(color: borderColor, width: 1.0),
          borderRadius: BorderRadius.circular(20),
        ),
        child: InkWell(
          onTap: _toggleDropdown,
          borderRadius: BorderRadius.circular(20),
          child: Stack(
            children: [
              Row(
                children: [
                  SizedBox(width: widget.showCross && isSelected ? 32 : 16),
                  Expanded(
                    child: Align(
                      alignment: Alignment.centerLeft,
                      child: Text(
                        selectedItem?.value.toString() ?? widget.hint,
                        style: AppFonts.regularTextStyle(
                          14,
                          color: AppTheme.black,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.only(right: 12),
                    child: Icon(
                      _isOpen
                          ? Icons.keyboard_arrow_up
                          : Icons.keyboard_arrow_down,
                      color: isSelected
                          ? AppTheme.selectedComboBoxBorder
                          : Colors.grey,
                      size: 20,
                    ),
                  ),
                ],
              ),
              if (widget.showCross && isSelected)
                Positioned(
                  left: 8,
                  top: 0,
                  bottom: 0,
                  child: Center(
                    child: GestureDetector(
                      onTap: () {
                        widget.onChanged('Select');
                      },
                      child: Container(
                        width: 18,
                        height: 18,
                        decoration: const BoxDecoration(
                          color: Colors.grey,
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.close,
                          size: 14,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
