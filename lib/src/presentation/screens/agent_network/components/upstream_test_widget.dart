import 'package:flutter/material.dart';
import '../../../../domain/models/agent.dart';
import '../../../../domain/models/broker.dart';
import 'upstream_users_section.dart';

/// Test widget to verify upstream users functionality
/// This can be used for testing different scenarios
class UpstreamTestWidget extends StatelessWidget {
  const UpstreamTestWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Upstream Users Test')),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Scenario 1: 10 Users (Collapsed/Expanded)',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            UpstreamUsersSection(
              upstreamUsers: _getScenario1Users(),
              broker: _getSampleBroker(),
            ),
            const SizedBox(height: 32),

            const Text(
              'Scenario 2: 1 User (Direct)',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            UpstreamUsersSection(
              upstreamUsers: _getScenario2Users(),
              broker: _getSampleBroker(),
            ),
            const SizedBox(height: 32),

            const Text(
              'Scenario 3: 2 Users (Direct)',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            UpstreamUsersSection(
              upstreamUsers: _getScenario3Users(),
              broker: _getSampleBroker(),
            ),
            const SizedBox(height: 32),

            const Text(
              'Scenario 4: No Users (Hidden)',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            UpstreamUsersSection(
              upstreamUsers: const [],
              broker: _getSampleBroker(),
            ),
          ],
        ),
      ),
    );
  }

  List<Agent> _getScenario1Users() {
    return List.generate(
      10,
      (index) => Agent(
        id: 'upstream_${index + 1}',
        name: 'User ${index + 1}',
        sales: 0,
        amount: 50000.0,
        commission: 5000.0,
        contact: '+1234567890',
        email: 'user${index + 1}@example.com',
        agents: [],
        color: Colors.blue,
        imageUrl: '',
        joinDate: DateTime.now(),
        state: 'CA',
        city: 'LA',
        level: 'Level ${index + 1}',
        totalDeals: 10,
        earning: 5000.0,
        status: true,
        totalAgents: 5,
        soldHomes: 8,
        totalSales: 15,
        role: 'Manager',
      ),
    );
  }

  List<Agent> _getScenario2Users() {
    return [
      Agent(
        id: 'upstream_1',
        name: 'Immediate Parent',
        sales: 0,
        amount: 75000.0,
        commission: 7500.0,
        contact: '+1234567890',
        email: '<EMAIL>',
        agents: [],
        color: Colors.blue,
        imageUrl: '',
        joinDate: DateTime.now(),
        state: 'CA',
        city: 'LA',
        level: 'Senior',
        totalDeals: 25,
        earning: 7500.0,
        status: true,
        totalAgents: 12,
        soldHomes: 20,
        totalSales: 30,
        role: 'Senior Manager',
      ),
    ];
  }

  List<Agent> _getScenario3Users() {
    return [
      Agent(
        id: 'upstream_1',
        name: 'Root Parent',
        sales: 0,
        amount: 100000.0,
        commission: 10000.0,
        contact: '+1234567890',
        email: '<EMAIL>',
        agents: [],
        color: Colors.blue,
        imageUrl: '',
        joinDate: DateTime.now(),
        state: 'CA',
        city: 'LA',
        level: 'Executive',
        totalDeals: 50,
        earning: 10000.0,
        status: true,
        totalAgents: 25,
        soldHomes: 40,
        totalSales: 60,
        role: 'Executive',
      ),
      Agent(
        id: 'upstream_2',
        name: 'Immediate Parent',
        sales: 0,
        amount: 75000.0,
        commission: 7500.0,
        contact: '+1234567890',
        email: '<EMAIL>',
        agents: [],
        color: Colors.blue,
        imageUrl: '',
        joinDate: DateTime.now(),
        state: 'CA',
        city: 'LA',
        level: 'Senior',
        totalDeals: 25,
        earning: 7500.0,
        status: true,
        totalAgents: 12,
        soldHomes: 20,
        totalSales: 30,
        role: 'Senior Manager',
      ),
    ];
  }

  Broker _getSampleBroker() {
    return Broker(
      id: 'broker_1',
      name: 'Sample Broker',
      sales: 100,
      imageUrl: '',
      contact: '+1234567890',
      email: '<EMAIL>',
      totalAgents: 100,
      agents: [],
      totalSalesRevenue: 1000000.0,
      totalCommission: 100000.0,
      color: Colors.blue,
      joinDate: DateTime.now(),
      role: 'Broker',
    );
  }
}
