import 'dart:async';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import '../../../core/config/app_strings.dart' as AppStrings;
import '../../../core/config/app_strings.dart';
import '../../../core/config/constants.dart';
import '../../../core/config/responsive.dart';
import '../../../core/navigation/web_router.dart';
import '../../../core/network/api_consts.dart';
import '../../../core/theme/app_fonts.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/utils/app_snack_bar.dart';
import '../../../core/utils/dotted_line_painter.dart';
import '../../cubit/sales_details/sales_details_cubit.dart';
import '../../cubit/user/user_cubit.dart';
import '../../shared/components/elevated_button.dart';

class UploadDocumentScreen extends HookWidget {
  final Function(String)? handleSaleSelection;
  UploadDocumentScreen({super.key, this.handleSaleSelection});

  final selectedIndex = ValueNotifier(0);
  final _formKey = GlobalKey<FormState>();
  final ValueNotifier<double> uploadProgress = ValueNotifier(0.0);

  final ValueNotifier<PlatformFile?> docFile = ValueNotifier(null);
  final ValueNotifier<bool> showFileUploadError = ValueNotifier(false);
  final ValueNotifier<String> uploadStatus = ValueNotifier('');
  final ValueNotifier<bool> isUploading = ValueNotifier(false);

  // Timer for progress simulation
  Timer? _progressTimer;

  @override
  Widget build(BuildContext context) {
    final isMobile = Responsive.isMobile(context);

    // return Scaffold(
    //   body: Stack(
    //     children: [
    //       /// 🔹 Background Image
    //       Positioned.fill(
    //         child: Image.asset(
    //           "../assets/images/register_bg.png",
    //           fit: BoxFit.cover,
    //         ),
    //       ),

    /// 🔹 Foreground Card Content
    ///
    ///

    useEffect(() {
      // Clear file and error state when navigating back
      return () {
        docFile.value = null;
        showFileUploadError.value = false;
      };
    }, []);

    return Center(
      child: Container(
        constraints: BoxConstraints(maxWidth: isMobile ? double.infinity : 600),
        margin: const EdgeInsets.all(defaultPadding),
        decoration: BoxDecoration(
          color: AppTheme.roundIconColor,
          borderRadius: BorderRadius.circular(25),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.08),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: ValueListenableBuilder(
          valueListenable: isUploading,
          builder: (context, value, child) {
            return Stack(
              children: [
                Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _formHeader(context),
                    _formContent(isMobile, context),
                  ],
                ),
                if (isUploading.value) ...[
                  Positioned.fill(
                    child: Container(
                      color: Colors.transparent,
                      child: Center(
                        child: CircularProgressIndicator(
                          color: Colors.transparent,
                        ),
                      ),
                    ),
                  ),
                ],
              ],
            );
          },
        ),
      ),
      //     ),
      //   ],
      // ),
    );
  }

  /// --- HEADER (blue rounded top) ---
  Container _formHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: defaultPadding * 2,
        vertical: defaultPadding * 2,
      ),
      decoration: BoxDecoration(
        color: AppTheme.roundIconColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(25),
          topRight: Radius.circular(25),
        ),
      ),
      child: Center(
        child: Text(
          AppStrings.uploadClosingDocument,
          style: AppFonts.semiBoldTextStyle(22, color: Colors.white),
        ),
      ),
    );
  }

  Widget _formContent(bool isMobile, BuildContext context) {
    return ValueListenableBuilder<PlatformFile?>(
      valueListenable: docFile,
      builder: (context, file, _) {
        return ClipRRect(
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(10),
            topRight: Radius.circular(10),
            bottomLeft: Radius.circular(25),
            bottomRight: Radius.circular(25),
          ),
          child: Container(
            color: Colors.white,
            padding: const EdgeInsets.all(20),
            child: Form(
              key: _formKey,
              child: Column(
                children: [
                  Text(
                    AppStrings.selectAndUploadFile,
                    style: AppFonts.semiBoldTextStyle(
                      16,
                      color: AppTheme.primaryTextColor,
                    ),
                  ),
                  const SizedBox(height: 16),

                  /// Upload Field
                  _buildUploadField(
                    AppStrings.chooseFileOrDragDrop,
                    AppStrings.pdfFormatInfo,
                    docFile,
                    APIConsts.allowedFileExtensions,
                  ),

                  const SizedBox(height: 16),

                  /// Show Progress Section only when file selected
                  if (file != null) ...[
                    _fileUploadRow(),
                    const SizedBox(height: 20),
                  ],

                  const SizedBox(height: 20),

                  /// Footer Buttons
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      AppButton(
                        label: AppStrings.clear,
                        backgroundColor: AppTheme.scaffoldBgColor,
                        foregroundColor: AppTheme.primaryTextColor,
                        borderRadius: 25,
                        padding: EdgeInsets.symmetric(
                          horizontal: defaultPadding * 2,
                          vertical: defaultPadding * 1.2,
                        ),
                        useMinSize: true,
                        onPressed: docFile.value == null
                            ? null
                            : () {
                                docFile.value = null;
                                showFileUploadError.value = false;
                              },
                      ),
                      const SizedBox(width: defaultPadding),
                      AppButton(
                        label: AppStrings.uploadAndContinue,
                        backgroundColor: AppTheme.roundIconColor,
                        foregroundColor: Colors.white,
                        borderRadius: 25,
                        padding: const EdgeInsets.symmetric(
                          horizontal: defaultPadding * 2,
                          vertical: defaultPadding * 1.2,
                        ),
                        useMinSize: true,
                        onPressed: docFile.value == null
                            ? null
                            : () {
                                if (docFile.value == null) {
                                  showFileUploadError.value = true;
                                } else {
                                  _uploadDocument(context);
                                }
                              },
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildUploadField(
    String hintText,
    String formatText,
    ValueNotifier<PlatformFile?> fileNotifier,
    List<String> allowedExtensions,
  ) {
    return ValueListenableBuilder<bool>(
      valueListenable: showFileUploadError,
      builder: (context, hasError, child) {
        return ValueListenableBuilder<PlatformFile?>(
          valueListenable: fileNotifier,
          builder: (context, file, child) {
            final isSmallMobile = Responsive.isSmallMobile(context);

            // Determine border color based on validation state
            Color borderColor = Colors.grey;
            if (file != null) {
              borderColor = Colors.green.shade200;
            } else if (hasError) {
              borderColor = Colors.red;
            }

            return buildDottedBorderContainerWithRadius(
              borderRadius: 25.0,
              borderColor: borderColor,
              child: Container(
                width: double.infinity,
                height: isSmallMobile ? 100 : 120,
                padding: EdgeInsets.all(
                  isSmallMobile ? defaultPadding / 2 : defaultPadding,
                ),
                decoration: BoxDecoration(
                  color: file != null
                      ? Colors.green.shade50
                      : hasError
                      ? Colors.red.shade50
                      : AppTheme.docUploadBgColor,
                  borderRadius: BorderRadius.circular(25),
                  border: file != null
                      ? Border.all(color: Colors.green.shade200)
                      : hasError
                      ? Border.all(color: Colors.red.shade200)
                      : null,
                ),
                child: file != null
                    ? Stack(
                        children: [
                          Center(
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.check_circle,
                                  color: Colors.green,
                                  size: isSmallMobile ? 16 : 20,
                                ),
                                SizedBox(width: isSmallMobile ? 8 : 12),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Text(
                                        file.name,
                                        style: AppFonts.mediumTextStyle(
                                          isSmallMobile ? 12 : 14,
                                          color: Colors.green.shade700,
                                        ),
                                        overflow: TextOverflow.ellipsis,
                                        textAlign: TextAlign.center,
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        '${(file.size / 1024).toStringAsFixed(1)} ${AppStrings.fileSizeKB}',
                                        style: AppFonts.regularTextStyle(
                                          isSmallMobile ? 10 : 12,
                                          color: Colors.green.shade600,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Positioned(
                            top: 0,
                            right: 0,
                            child: GestureDetector(
                              onTap: () {
                                fileNotifier.value = null;
                                // Reset validation error when file is removed
                                showFileUploadError.value = false;
                              },
                              child: Icon(
                                Icons.close,
                                color: Colors.red,
                                size: isSmallMobile ? 16 : 20,
                              ),
                            ),
                          ),
                        ],
                      )
                    : Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          ElevatedButton.icon(
                            onPressed: () => _showFilePickerOptions(
                              context,
                              fileNotifier,
                              allowedExtensions,
                            ),
                            icon: Image.asset(
                              '$iconAssetpath/upload.png',
                              height: isSmallMobile ? 14 : 16,
                              width: isSmallMobile ? 14 : 16,
                            ),
                            label: Text(
                              AppStrings.browseFile,
                              style: AppFonts.mediumTextStyle(
                                isSmallMobile ? 12 : 14,
                                color: AppTheme.black,
                              ),
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.white,
                              foregroundColor: AppTheme.primaryTextColor,
                              elevation: 0,
                              padding: EdgeInsets.symmetric(
                                horizontal: isSmallMobile ? 8 : 12,
                                vertical: isSmallMobile ? 4 : 8,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                                side: BorderSide(color: AppTheme.borderColor),
                              ),
                            ),
                          ),
                          SizedBox(height: isSmallMobile ? 4 : 8),
                          Text(
                            hintText,
                            textAlign: TextAlign.center,
                            style: AppFonts.mediumTextStyle(
                              isSmallMobile ? 10 : 12,
                              color: AppTheme.black,
                            ),
                          ),
                          Text(
                            formatText,
                            textAlign: TextAlign.center,
                            style: AppFonts.regularTextStyle(
                              isSmallMobile ? 9 : 12,
                              color: AppTheme.ternaryTextColor,
                            ),
                          ),
                        ],
                      ),
              ),
            );
          },
        );
      },
    );
  }

  /// --- File Upload Row (with progress bar & remove button) ---
  Widget processingStatusText(double progress, String status, int size) {
    return HookBuilder(
      builder: (context) {
        final messages = [
          AppStrings.processingInitiated,
          AppStrings.analyzingFile,
          AppStrings.optimizingUpload,
          AppStrings.almostDone,
        ];

        final index = useState(0);

        // cycle messages every 2 seconds
        useEffect(() {
          final timer = Timer.periodic(const Duration(seconds: 2), (_) {
            index.value = (index.value + 1) % messages.length;
          });
          return timer.cancel;
        }, []);

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '${((docFile.value?.size ?? 0) / 1024).toStringAsFixed(1)} KB · ${(progress * 100).toInt()}% ${messages[index.value]}',
              style: AppFonts.regularTextStyle(
                11,
                color: Colors.orange, // stage message color
              ),
            ),
            const SizedBox(height: 2),
            Text(
              AppStrings.fileSizeLager,
              style: AppFonts.regularTextStyle(
                11,
                color: Colors.grey[600]!, // secondary line in different color
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _fileUploadRow() {
    return ValueListenableBuilder<double>(
      valueListenable: uploadProgress,
      builder: (context, progress, _) {
        return ValueListenableBuilder<String>(
          valueListenable: uploadStatus,
          builder: (context, status, _) {
            return Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  const Icon(Icons.picture_as_pdf, color: Colors.red, size: 32),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          docFile.value?.name ?? '',
                          style: AppFonts.mediumTextStyle(
                            13,
                            color: AppTheme.primaryTextColor,
                          ),
                        ),
                        const SizedBox(height: 4),
                        LinearProgressIndicator(
                          value: progress,
                          backgroundColor: Colors.grey[300],
                          color:
                              status == AppStrings.uploadCompletedSuccessfully
                              ? Colors.green
                              : status == AppStrings.uploadFailed
                              ? Colors.red
                              : AppTheme.roundIconColor,
                          minHeight: 4,
                        ),
                        const SizedBox(height: 2),
                        status == AppStrings.processing
                            ? processingStatusText(
                                progress,
                                status,
                                docFile.value?.size ?? 0,
                              )
                            : Text(
                                "${((docFile.value?.size ?? 0) / 1024).toStringAsFixed(1)} KB · ${_getUploadStatusText(progress, status)}",
                                style: AppFonts.regularTextStyle(
                                  11,
                                  color: _getStatusColor(status),
                                ),
                              ),
                      ],
                    ),
                  ),
                  if (progress < 1.0 && status != AppStrings.uploading)
                    IconButton(
                      onPressed: () {
                        docFile.value = null;
                        uploadProgress.value = 0.0;
                        uploadStatus.value = '';
                      },
                      icon: const Icon(Icons.close, color: Colors.grey),
                    ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  String _getUploadStatusText(double progress, String status) {
    if (status == AppStrings.uploading) {
      return '${AppStrings.uploading} ${(progress * 100).toInt()}%';
    } else if (status == AppStrings.uploadCompletedSuccessfully) {
      return AppStrings.uploadCompletedSuccessfully;
    } else if (status == AppStrings.uploadFailed) {
      return AppStrings.uploadFailed;
    } else if (status == AppStrings.preparingUpload) {
      return '${AppStrings.preparingUpload} ${(progress * 100).toInt()}%';
    } else if (status == AppStrings.uploadingDocument) {
      return '${AppStrings.uploadingDocument} ${(progress * 100).toInt()}%';
    } else if (status == AppStrings.processing) {
      return '${AppStrings.processing} ${(progress * 100).toInt()}%';
    } else if (status == AppStrings.uploadCompleteProcessing) {
      return AppStrings.uploadCompleteProcessing;
    } else if (status == AppStrings.finalizingUpload) {
      return '${AppStrings.finalizingUpload} ${(progress * 100).toInt()}%';
    } else {
      return status.isEmpty ? AppStrings.readyToUpload : status;
    }
  }

  Color _getStatusColor(String status) {
    if (status == AppStrings.uploading ||
        status == '' ||
        status == AppStrings.preparingUpload ||
        status == AppStrings.uploadingDocument ||
        status == AppStrings.processing ||
        status == AppStrings.uploadCompleteProcessing ||
        status == AppStrings.finalizingUpload) {
      return Colors.grey[600]!;
    } else if (status == AppStrings.uploadCompletedSuccessfully) {
      return Colors.green;
    } else if (status == AppStrings.uploadFailed) {
      return Colors.red;
    } else {
      return Colors.grey[600]!;
    }
  }

  /// Show file picker options for iOS compatibility
  Future<void> _showFilePickerOptions(
    BuildContext context,
    ValueNotifier<PlatformFile?> fileNotifier,
    List<String> allowedExtensions,
  ) async {
    uploadProgress.value = 0.0; // Reset progress on error

    uploadStatus.value = '';
    if (kIsWeb) {
      // On web, directly use file picker
      return _pickFile(context, fileNotifier, allowedExtensions);
    }

    // On mobile, show options
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return SafeArea(
          child: Wrap(
            children: [
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: const Text(AppStrings.photoLibrary),
                onTap: () {
                  Navigator.pop(context);
                  _pickFromGallery(context, fileNotifier);
                },
              ),
              ListTile(
                leading: const Icon(Icons.camera_alt),
                title: const Text(AppStrings.camera),
                onTap: () {
                  Navigator.pop(context);
                  _pickFromCamera(context, fileNotifier);
                },
              ),
              ListTile(
                leading: const Icon(Icons.folder),
                title: const Text(AppStrings.files),
                onTap: () {
                  Navigator.pop(context);
                  _pickFile(context, fileNotifier, allowedExtensions);
                },
              ),
              ListTile(
                leading: const Icon(Icons.cancel),
                title: const Text(AppStrings.cancel),
                onTap: () => Navigator.pop(context),
              ),
            ],
          ),
        );
      },
    );
  }

  /// Pick image from gallery using image_picker
  Future<void> _pickFromGallery(
    BuildContext context,
    ValueNotifier<PlatformFile?> fileNotifier,
  ) async {
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80,
      );

      if (image != null) {
        // Convert XFile to PlatformFile
        final file = File(image.path);
        final bytes = await file.readAsBytes();

        final platformFile = PlatformFile(
          name: image.name,
          size: bytes.length,
          path: image.path,
          bytes: bytes,
        );

        fileNotifier.value = platformFile;
        showFileUploadError.value = false;
      }
    } catch (e) {
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text(
            '${AppStrings.failedToPickImageFromGallery}: ${e.toString()}',
          ),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// Pick image from camera using image_picker
  Future<void> _pickFromCamera(
    BuildContext context,
    ValueNotifier<PlatformFile?> fileNotifier,
  ) async {
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.camera,
        imageQuality: 80,
      );

      if (image != null) {
        // Convert XFile to PlatformFile
        final file = File(image.path);
        final bytes = await file.readAsBytes();

        final platformFile = PlatformFile(
          name: image.name,
          size: bytes.length,
          path: image.path,
          bytes: bytes,
        );

        fileNotifier.value = platformFile;
        showFileUploadError.value = false;
      }
    } catch (e) {
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text('${AppStrings.failedToCaptureImage}: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _pickFile(
    BuildContext context,
    ValueNotifier<PlatformFile?> fileNotifier,
    List<String> allowedExtensions,
  ) async {
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    try {
      FilePickerResult? result;
      if (kIsWeb) {
        // Web-specific configuration
        result = await FilePicker.platform.pickFiles(
          type: FileType.custom,
          allowedExtensions: allowedExtensions,
          allowMultiple: false,
          withData: true,
        );
      } else {
        // Mobile/Desktop configuration - try different approaches for iOS
        try {
          result = await FilePicker.platform.pickFiles(
            type: FileType.custom,
            allowedExtensions: allowedExtensions,
            allowMultiple: false,
            withData: false,
          );
        } catch (e) {
          // Fallback to any file type if custom fails on iOS
          result = await FilePicker.platform.pickFiles(
            type: FileType.any,
            allowMultiple: false,
            withData: false,
            compressionQuality: 80,
          );
        }
      }

      debugPrint('File picker result: ${result?.files.length ?? 0} files');

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;
        debugPrint(
          'Selected file: ${file.name}, size: ${file.size}, extension: ${file.extension}',
        );
        // Check if file size exceeds 25MB
        if (file.size > maxFileSizeInBytes) {
          AppSnackBar.showSnackBar(
            context,
            AppStrings.fileSizeExceeded,
            SnackBarType.error,
          );
          return;
        }

        if (file.extension != 'pdf') {
          scaffoldMessenger.showSnackBar(
            SnackBar(
              content: Text(AppStrings.selectPdfFile),
              backgroundColor: Colors.red,
            ),
          );
          return;
        }
        // Validate file type for mobile (since we use FileType.any)
        if (!kIsWeb) {
          final extension = file.extension?.toLowerCase();
          if (extension == null || !allowedExtensions.contains(extension)) {
            debugPrint(
              'Invalid file type: $extension. Allowed: $allowedExtensions',
            );
            scaffoldMessenger.showSnackBar(
              SnackBar(
                content: Text(
                  '${AppStrings.pleaseSelectValidFileType} ${allowedExtensions.join(', ')}',
                ),
              ),
            );
            return;
          }
        }

        // Validate file based on platform
        if (kIsWeb) {
          if (file.bytes != null) {
            fileNotifier.value = file;
            // Clear validation error when file is selected
            showFileUploadError.value = false;
          } else {
            debugPrint('Web: File bytes not available');
          }
        } else {
          if (file.path != null) {
            fileNotifier.value = file;
            // Clear validation error when file is selected
            showFileUploadError.value = false;
          } else {
            debugPrint('Mobile: File path not available');
          }
        }
      } else {
        debugPrint('No file selected or result is null');
      }
    } catch (e) {
      debugPrint('$errorPickingFile: $e');
      // Show user-friendly error message
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text('$failedToOpenFilePicker: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// Simulates gradual progress during upload
  void _startProgressSimulation() {
    _progressTimer?.cancel(); // Cancel any existing timer

    const totalDuration = Duration(seconds: 8); // Total expected upload time
    const updateInterval = Duration(milliseconds: 100); // Update every 100ms
    final totalUpdates =
        totalDuration.inMilliseconds / updateInterval.inMilliseconds;

    int currentUpdate = 0;

    _progressTimer = Timer.periodic(updateInterval, (timer) {
      if (!isUploading.value) {
        timer.cancel();
        return;
      }

      currentUpdate++;

      // Create a realistic progress curve (slower at start and end, faster in middle)
      double normalizedProgress = currentUpdate / totalUpdates;

      // Use a sigmoid-like curve for more realistic progress
      double simulatedProgress;
      if (normalizedProgress < 0.1) {
        // Slow start (0-10%)
        simulatedProgress = normalizedProgress * 0.5; // 0-5%
      } else if (normalizedProgress < 0.8) {
        // Fast middle section (10-80% -> 5-85%)
        simulatedProgress = 0.05 + (normalizedProgress - 0.1) * 0.8 / 0.7;
      } else {
        // Slow end (80-100% -> 85-95%)
        simulatedProgress = 0.85 + (normalizedProgress - 0.8) * 0.1 / 0.2;
      }

      // Don't exceed 95% until actual upload completes
      simulatedProgress = simulatedProgress.clamp(0.0, 0.95);

      // Only update if simulated progress is higher than current
      if (simulatedProgress > uploadProgress.value) {
        uploadProgress.value = simulatedProgress;

        // Update status messages based on progress
        if (simulatedProgress < 0.3) {
          uploadStatus.value = AppStrings.preparingUpload;
        } else if (simulatedProgress < 0.7) {
          uploadStatus.value = AppStrings.uploadingDocument;
        } else {
          uploadStatus.value = AppStrings.processing;
        }
      }

      // Stop simulation at 95% and let actual upload progress take over
      if (simulatedProgress >= 0.95) {
        timer.cancel();
      }
    });
  }

  /// Stops the progress simulation
  void _stopProgressSimulation() {
    _progressTimer?.cancel();
    _progressTimer = null;
  }

  void _uploadDocument(BuildContext context) async {
    if (docFile.value == null) {
      showFileUploadError.value = true;
      return;
    }

    isUploading.value = true;
    uploadProgress.value = 0.0;
    uploadStatus.value = AppStrings.uploading;

    // Start gradual progress simulation
    _startProgressSimulation();

    final user = context.read<UserCubit>().state.user;
    final uploadFilePayload = {
      "userId": user?.userId ?? '',
      "categoryType": APIConsts.salesCategoryType,
      "documentType": APIConsts.salesDocType,
      "file": docFile.value,
      "onProgress": (int sent, int total) {
        if (total != 0) {
          final actualProgress = sent / total;
          final currentSimulatedProgress = uploadProgress.value;

          // Only use actual progress if it's significantly higher than simulated
          // and we're past the initial preparation phase (30%)
          if (actualProgress > 0.95 && currentSimulatedProgress >= 0.85) {
            // Near completion, use actual progress
            uploadProgress.value = actualProgress;
            if (actualProgress >= 1.0) {
              uploadStatus.value = AppStrings.uploadCompleteProcessing;
            } else {
              uploadStatus.value = AppStrings.finalizingUpload;
            }
          } else if (actualProgress > currentSimulatedProgress &&
              currentSimulatedProgress >= 0.3) {
            // In middle phase, blend actual and simulated progress
            final blendedProgress =
                (currentSimulatedProgress + actualProgress) / 2;
            uploadProgress.value = blendedProgress.clamp(
              currentSimulatedProgress,
              0.95,
            );
          }
          // Otherwise, let simulation handle the progress

          WidgetsBinding.instance.addPostFrameCallback((_) {});
        }
      },
    };

    try {
      final saleDocCubit = context.read<SalesDetailsCubit>();
      await saleDocCubit.uploadDocFile(uploadFilePayload);
      if (saleDocCubit.state is SalesClosingDocumentFileUploaded) {
        final state = saleDocCubit.state as SalesClosingDocumentFileUploaded;
        final String salesId = state.salesId;

        // Stop simulation and complete progress
        _stopProgressSimulation();
        uploadProgress.value = 1.0;
        uploadStatus.value = AppStrings.uploadCompletedSuccessfully;

        await AppSnackBar.showSnackBar(
          context,
          state.message,
          SnackBarType.success,
          timeout: Duration(seconds: 1),
        );

        // if (handleSaleSelection != null) {
        //   handleSaleSelection!(salesId);
        // }
        isUploading.value = false;
        context.go(
          AppRoutes.saleReviewDoc.path,
          extra: {'salesId': salesId, 'isAgentEdit': true},
        );
        docFile.value = null;
      } else if (saleDocCubit.state is SalesDetailsError) {
        // Stop simulation and reset progress on error
        _stopProgressSimulation();
        uploadProgress.value = 0.0;
        uploadStatus.value = AppStrings.uploadFailed;
        final error = saleDocCubit.state as SalesDetailsError;
        isUploading.value = false;

        await AppSnackBar.showSnackBar(
          context,
          error.message,
          SnackBarType.error,
        );
      } else {
        // Stop simulation and reset progress on error
        _stopProgressSimulation();
        uploadProgress.value = 0.0;
        uploadStatus.value = AppStrings.uploadFailed;
        isUploading.value = false;
        await AppSnackBar.showSnackBar(
          context,
          AppStrings.errorOccured,
          SnackBarType.error,
        );
      }
    } catch (e) {
      // Stop simulation and reset progress on error
      _stopProgressSimulation();
      isUploading.value = false;
      uploadProgress.value = 0.0;
      uploadStatus.value = AppStrings.uploadFailed;
      await AppSnackBar.showSnackBar(
        context,
        AppStrings.errorOccured,
        SnackBarType.error,
      );
    }
  }
}
