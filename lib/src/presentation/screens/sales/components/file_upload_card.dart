import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:dotted_border/dotted_border.dart';
import '../../../../core/config/app_strings.dart' as AppStrings;
import 'file_drop_zone.dart';
import 'file_info_display.dart';
import 'upload_actions.dart';

class FileUploadCard extends StatelessWidget {
  final PlatformFile? selectedFile;
  final bool isUploading;
  final double uploadProgress;
  final VoidCallback onPickFile;
  final VoidCallback onClearFile;
  final VoidCallback onUploadAndContinue;

  const FileUploadCard({
    super.key,
    required this.selectedFile,
    required this.isUploading,
    required this.uploadProgress,
    required this.onPickFile,
    required this.onClearFile,
    required this.onUploadAndContinue,
  });

  @override
  Widget build(BuildContext context) {
    final isWeb = MediaQuery.of(context).size.width > 600;
    final contentPadding = isWeb
        ? const EdgeInsets.all(24)
        : const EdgeInsets.all(16);

    return Card(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Content area
          Padding(
            padding: contentPadding,
            child: Column(
              children: [
                // Title and subtitle
                Text(
                  AppStrings.uploadFile,
                  style: Theme.of(context).textTheme.headlineMedium,
                ),
                const SizedBox(height: 8),
                Text(
                  AppStrings.selectAndUploadFile,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                SizedBox(height: isWeb ? 24 : 16),

                // File drop zone (always visible)
                FileDropZone(
                  onPickFile: onPickFile,
                  hasSelectedFile: selectedFile != null,
                ),

                // File info display (show when file is selected)
                if (selectedFile != null) ...[
                  SizedBox(height: isWeb ? 16 : 12),
                  FileInfoDisplay(
                    file: selectedFile!,
                    isUploading: isUploading,
                    uploadProgress: uploadProgress,
                    onClose: onClearFile,
                  ),
                ],

                SizedBox(height: isWeb ? 24 : 16),

                // Action buttons
                UploadActions(
                  hasFile: selectedFile != null,
                  isUploading: isUploading,
                  onClear: onClearFile,
                  onUploadAndContinue: onUploadAndContinue,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
