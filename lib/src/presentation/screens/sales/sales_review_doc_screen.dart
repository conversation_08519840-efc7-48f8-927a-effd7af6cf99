import 'dart:convert';
import 'dart:io';
import 'dart:ui' as ui;
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:http_parser/http_parser.dart';
import 'package:intl/intl.dart';
import '../../../core/utils/app_snack_bar.dart';
import '../../shared/components/customDialogues/alert_dialogue.dart';
import '/src/core/network/api_config.dart';
import '/src/data/repository/auth_data_repository.dart';
import '/src/domain/models/representing_type_model.dart';
import 'package:open_filex/open_filex.dart';
import '../../../core/navigation/web_router.dart';
import '../../../core/utils/commission_split_formatter.dart';
import '../../../core/utils/currency_input_formatter.dart';
import '../../../core/utils/format_currency_dollar.dart';
import '../../../domain/models/lead_source_model.dart';
import '../../../domain/models/transaction_type_model.dart';
import '/src/core/enum/user_role.dart';
import 'package:universal_html/html.dart' as html;
import 'dart:ui_web' as ui_web;
import '../../../core/network/api_consts.dart';
import '../../../core/utils/date_formatter.dart';
import '../../../core/utils/validators.dart';
import '../../cubit/sales_details/sales_details_cubit.dart';
import '../../cubit/user/user_cubit.dart';
import '../../shared/components/calendar/custom_calendar_widget.dart';
import '../../shared/components/elevated_button.dart';
import '../../../core/config/app_strings.dart';
import '../../../core/utils/dotted_line_painter.dart';
import '../../shared/components/radio_btn.dart';
import '../../../core/config/app_strings.dart' as AppStrings;
import '../../../core/config/constants.dart';
import '../../../core/config/responsive.dart';
import '../../../core/theme/app_fonts.dart';
import '../../../core/theme/app_theme.dart';
import 'package:signature/signature.dart';
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:path_provider/path_provider.dart';
import 'package:http/http.dart' as http;

// Add after imports, before SalesReviewDocScreen class
class FormStateManager {
  static final FormStateManager _instance = FormStateManager._internal();
  factory FormStateManager() => _instance;
  FormStateManager._internal();

  final Map<String, String> _formState = {};

  void saveState(Map<String, String> state) {
    _formState.clear();
    _formState.addAll(state);
  }

  Map<String, String> getState() => Map.from(_formState);
}

enum SignatureSource { uploaded, drawn }

class SalesReviewDocScreen extends HookWidget {
  final bool enableEditing;
  final String salesID;
  final bool
  isAgentEdit; // True if the logged-in user is an agent and allowed to edit their uploaded documents

  String salesTypeSelected = 'Traditional';
  SignatureSource? _selectedSignatureSource;
  SalesReviewDocScreen({
    Key? key,
    this.enableEditing = true,
    required this.salesID,
    required this.isAgentEdit,
    // required this.allowAgentEdit,
  }) : super(key: key);

  final selectedIndex = ValueNotifier(0);
  bool isEnableEdit = false;
  bool isMobile = false;
  final state = FormStateManager().getState();
  late final isEditable = ValueNotifier<bool>(
    state['isEditable']?.toLowerCase() == 'false' ? false : true,
  );

  final isDocAlreadySigned = ValueNotifier<bool>(
    false,
  ); // Form controllers with state initialization
  final shouldShowEditButton = ValueNotifier<bool>(
    FormStateManager().getState()['shouldShowEditButton']?.toLowerCase() ==
            'true'
        ? true
        : false,
  ); // Form controllers with state initialization
  final transactionIdController = TextEditingController(
    text: FormStateManager().getState()['transactionId'] ?? '',
  );
  final transactionNameController = TextEditingController(
    text: FormStateManager().getState()['transactionName'] ?? '',
  );
  final addressController = TextEditingController(
    text: FormStateManager().getState()['address'] ?? '',
  );
  final salesVolumeController = TextEditingController(
    text: FormStateManager().getState()['salesVolume'] ?? '',
  );
  final moneyReceivedController = TextEditingController(
    text: FormStateManager().getState()['moneyReceived'] ?? '',
  );
  final dateDepositedController = TextEditingController(
    text: FormStateManager().getState()['dateDeposited'] ?? '',
  );
  final grossCommissionController = TextEditingController(
    text: FormStateManager().getState()['grossCommission'] ?? '',
  );
  final commissionSplitController = TextEditingController(
    text: FormStateManager().getState()['commissionSplit'] ?? '',
  );
  final dateReleasedController = TextEditingController(
    text: FormStateManager().getState()['dateReleased'] ?? '',
  );
  final listingDateController = TextEditingController(
    text: FormStateManager().getState()['listingDate'] ?? '',
  );
  final expirationDateController = TextEditingController(
    text: FormStateManager().getState()['expirationDate'] ?? '',
  );
  final closingDateController = TextEditingController(
    text: FormStateManager().getState()['closingDate'] ?? '',
  );
  final legalDescriptionController = TextEditingController(
    text: FormStateManager().getState()['legalDescription'] ?? '',
  );
  final escrowNumberController = TextEditingController(
    text: FormStateManager().getState()['escrowNumber'] ?? '',
  );
  final dateReceivedController = TextEditingController(
    text: FormStateManager().getState()['dateReceived'] ?? '',
  );
  final amountReleasedController = TextEditingController(
    text: FormStateManager().getState()['amountReleased'] ?? '',
  );
  final firstNameController = TextEditingController(
    text: FormStateManager().getState()['firstName'] ?? '',
  );
  final lastNameController = TextEditingController(
    text: FormStateManager().getState()['lastName'] ?? '',
  );
  final emailController = TextEditingController(
    text: FormStateManager().getState()['email'] ?? '',
  );
  final companyPhoneNumberController = TextEditingController(
    text: FormStateManager().getState()['companyPhoneNumber'] ?? '',
  );
  final contactAddressController = TextEditingController(
    text: FormStateManager().getState()['contactAddress'] ?? '',
  );
  final leadSourceController = TextEditingController(
    text: FormStateManager().getState()['leadSource'] ?? '',
  );
  final representedContactController = TextEditingController(
    text: FormStateManager().getState()['representedContact'] ?? '',
  );
  // String closingDocFileName =
  //     FormStateManager().getState()['closingDocFileName'] ?? '';
  ValueNotifier<String> closingDocFileName = ValueNotifier(
    FormStateManager().getState()['closingDocFileName'] ?? '',
  );

  String closingDocFileUrl =
      FormStateManager().getState()['closingDocFileUrl'] ?? '';
  SignatureController _signatureController = SignatureController();

  final ValueNotifier<PlatformFile?> signatureFile =
      ValueNotifier<PlatformFile?>(loadSignatureFromState());
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  // Add calendar state variables
  final ValueNotifier<bool> showCalendar = ValueNotifier<bool>(false);
  final ValueNotifier<String?> activeDateField = ValueNotifier<String?>(null);
  final ValueNotifier<DateTime> currentMonth = ValueNotifier<DateTime>(
    DateTime.now(),
  );
  final ValueNotifier<DateTime?> selectedDate = ValueNotifier<DateTime?>(null);
  final ValueNotifier<String> selectedQuickOption = ValueNotifier<String>('');
  final ValueNotifier<DateTime?> rangeStart = ValueNotifier<DateTime?>(null);
  final ValueNotifier<DateTime?> rangeEnd = ValueNotifier<DateTime?>(null);
  final ValueNotifier<bool> isCustomRangeMode = ValueNotifier<bool>(false);
  final ValueNotifier<DateTime?> customRangeStart = ValueNotifier<DateTime?>(
    null,
  );
  final ValueNotifier<DateTime?> customRangeEnd = ValueNotifier<DateTime?>(
    null,
  );

  // Add field position tracking
  final ValueNotifier<Offset?> fieldPosition = ValueNotifier<Offset?>(null);
  final ValueNotifier<GlobalKey?> activeFieldKey = ValueNotifier<GlobalKey?>(
    null,
  );

  // Overlay entry for calendar
  OverlayEntry? _calendarOverlayEntry;

  // Add GlobalKeys for date fields
  final GlobalKey dateDepositedKey = GlobalKey();
  final GlobalKey dateReleasedKey = GlobalKey();
  final GlobalKey listingDateKey = GlobalKey();
  final GlobalKey expirationDateKey = GlobalKey();
  final GlobalKey closingDateKey = GlobalKey();
  final GlobalKey dateReceivedKey = GlobalKey();
  // Date field constants
  static const String dateDeposited = 'Date Deposited';
  static const String dateReleased = 'Date Released';
  static const String listingDate = 'Listing Date';
  static const String expirationDateString = 'Expiration Date(of Listing)';
  static const String closingDate = 'Closing Date';
  static const String dateReceived = 'Date Received';
  String salesAgentId = '';
  String propertyType = '';
  String propertyDescription = '';
  String lenderInfo = '';
  String escrowCompany = '';
  // String transactionType = '';
  final ValueNotifier<String?> transactionTypeString = ValueNotifier<String?>(
    FormStateManager().getState()['transactionTypeString'] ?? '',
  );
  final ValueNotifier<int?> transactionTypeInt = ValueNotifier<int?>(
    int.tryParse(FormStateManager().getState()['transactionTypeInt'] ?? '-1') ??
        -1,
  );
  final ValueNotifier<String?> transactionIDString = ValueNotifier<String?>(
    FormStateManager().getState()['transactionId'] ?? '',
  );

  final ValueNotifier<String?> representingTypeString = ValueNotifier<String?>(
    FormStateManager().getState()['representingTypeString'] ?? '',
  );

  // Keep track of the selected TransactionType model
  final ValueNotifier<TransactionType?> selectedTransactionType =
      ValueNotifier<TransactionType?>(null);
  // Keep track of the selected TransactionType model
  final ValueNotifier<RepresentingTypes?> selectedRepresentingType =
      ValueNotifier<RepresentingTypes?>(null);
  // Keep track of the selected TransactionType model
  final ValueNotifier<LeadSource?> selectedLeadSourceType =
      ValueNotifier<LeadSource?>(null);

  @override
  Widget build(BuildContext context) {
    final state = context.watch<SalesDetailsCubit>().state;
    if (state is SalesDetailsFormState) {
      selectedRepresentingType.value = state.selectedRepresentingType;
      selectedTransactionType.value = state.selectedTransactionType;
      selectedLeadSourceType.value = state.selectedLeadSource;
      closingDocFileName.value = state.closingDocFileName ?? '';
      closingDocFileUrl = state.closingDocFileUrl ?? '';
    }
    isMobile = Responsive.isMobile(context);
    _signatureController = useMemoized(
      () => SignatureController(
        penStrokeWidth: 2,
        penColor: Colors.black,
        exportBackgroundColor: Colors.white,
      ),
    );

    final isBuyer = useState<bool?>(true);
    final saleType = useState<int>(0);

    useEffect(() {
      isEditable.value = isAgentEdit ? true : enableEditing;
      shouldShowEditButton.value = isAgentEdit
          ? false
          : shouldShowEditButton.value;
      return () => _signatureController.dispose();
    }, []);

    Future<void> initData() async {
      final saleDocCubit = context.read<SalesDetailsCubit>();

      // --- Fetch transaction types ---
      await saleDocCubit.fetchTransactionTypes();

      // --- Fetch lead source ---
      await saleDocCubit.fetchLeadSourceTypes();

      // --- Fetch representing types ---
      await saleDocCubit.fetchRepresentingTypes();
      // --- Fetch sales closing document details ---

      //  Now call sales closing document API only after above 3 are done
      await fetchSalesClosingDocumentDetails(context);
    }

    useEffect(() {
      Future.microtask(() async {
        await initData();
      });
    }, []);

    // Cleanup overlay on dispose
    useEffect(() {
      return () {
        _hideCalendarOverlay();
        FormStateManager().saveState({});
      };
    }, []);

    return ValueListenableBuilder(
      valueListenable: isEditable,
      builder: (context, value, child) {
        return _formWidget(
          context,
          isMobile,
          isBuyer,
          saleType,
          _signatureController,
        );
      },
    );
  }

  void _showCalendarOverlay(BuildContext context, String fieldLabel) {
    _hideCalendarOverlay(); // Remove any existing overlay

    _calendarOverlayEntry = OverlayEntry(
      builder: (context) => _buildCalendarOverlay(context, fieldLabel),
    );

    Overlay.of(context).insert(_calendarOverlayEntry!);
    showCalendar.value = true;
  }

  void _hideCalendarOverlay() {
    _calendarOverlayEntry?.remove();
    _calendarOverlayEntry = null;
    showCalendar.value = false;
  }

  void _updateFieldWithDate(DateTime date) {
    if (activeDateField.value != null) {
      final controller = _getControllerForField(activeDateField.value!);
      final formattedDate = AppDateFormatter.formatDateMMddyyyy(date);
      controller.text = formattedDate;
    }
  }

  Widget _buildCalendarOverlay(BuildContext context, String fieldLabel) {
    return Stack(
      children: [
        // Barrier to close calendar
        Positioned.fill(
          child: GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: _hideCalendarOverlay,
            child: Container(color: Colors.black.withOpacity(0.3)),
          ),
        ),

        // Calendar widget with dynamic positioning
        Positioned(
          top: _calculateCalendarTop(context),
          left: _calculateCalendarLeft(context),
          right: _calculateCalendarRight(context),
          child: ValueListenableBuilder<DateTime?>(
            valueListenable: selectedDate,
            builder: (context, selectedDateValue, child) {
              return ValueListenableBuilder<String>(
                valueListenable: selectedQuickOption,
                builder: (context, quickOption, child) {
                  return ValueListenableBuilder<DateTime>(
                    valueListenable: currentMonth,
                    builder: (context, currentMonthValue, child) {
                      return Material(
                        child: CustomCalendarWidget(
                          currentMonth: currentMonthValue,
                          selectedQuickOption: quickOption,
                          selectedDate: selectedDateValue,
                          rangeStart: rangeStart.value,
                          rangeEnd: rangeEnd.value,
                          isCustomRangeMode: isCustomRangeMode.value,
                          customRangeStart: customRangeStart.value,
                          customRangeEnd: customRangeEnd.value,
                          fromFilter: false,
                          isFutureDateHide: fieldLabel != expirationDateString,
                          onDateSelection: (date) {
                            selectedDate.value = date;
                            // _updateFieldWithDate(date);
                            // _hideCalendarOverlay();
                          },
                          onQuickSelection: (option) {
                            selectedQuickOption.value = option;
                          },
                          onNavigateMonth: (monthOffset) {
                            final newMonth = DateTime(
                              currentMonth.value.year,
                              currentMonth.value.month + monthOffset,
                            );
                            currentMonth.value = newMonth;
                          },
                          onCancel: _hideCalendarOverlay,
                          onApply: () {
                            if (selectedDate.value != null) {
                              _updateFieldWithDate(selectedDate.value!);
                            }
                            _hideCalendarOverlay();
                          },
                          isDateInSelectedRange: (date) {
                            if (rangeStart.value == null ||
                                rangeEnd.value == null) {
                              return false;
                            }
                            return date.isAfter(
                                  rangeStart.value!.subtract(
                                    const Duration(days: 1),
                                  ),
                                ) &&
                                date.isBefore(
                                  rangeEnd.value!.add(const Duration(days: 1)),
                                );
                          },
                          isRangeStartDate: (date) {
                            return rangeStart.value != null &&
                                date.year == rangeStart.value!.year &&
                                date.month == rangeStart.value!.month &&
                                date.day == rangeStart.value!.day;
                          },
                        ),
                      );
                    },
                  );
                },
              );
            },
          ),
        ),
      ],
    );
  }

  double _calculateCalendarTop(BuildContext context) {
    if (fieldPosition.value == null) {
      return MediaQuery.of(context).size.height * 0.2;
    }

    const fieldHeight = 56.0;
    const spacing = 8.0; // Small gap between field and calendar
    const calendarHeight = 400.0;
    const margin = 20.0;

    final screenHeight = MediaQuery.of(context).size.height;
    final fieldTop = fieldPosition.value!.dy;
    final fieldBottom = fieldTop + fieldHeight;

    // Calculate available space above and below the field in current viewport
    final spaceAbove = fieldTop - margin;
    final spaceBelow = screenHeight - fieldBottom - margin;

    double calendarTop;
    String positionReason = "";

    // Decision logic based on available space
    if (spaceBelow >= calendarHeight + spacing) {
      // Enough space below - position below the field
      calendarTop = fieldBottom + spacing;
      positionReason = "Below field - enough space";
    } else if (spaceAbove >= calendarHeight + spacing) {
      // Not enough space below but enough above - position above the field
      calendarTop = fieldTop - calendarHeight - spacing;
      positionReason = "Above field - not enough space below";
    } else if (spaceBelow > spaceAbove) {
      // More space below than above - position below with available space
      calendarTop = fieldBottom + spacing;
      positionReason = "Below field - more space than above";
    } else {
      // More space above than below - position above with available space
      calendarTop = fieldTop - calendarHeight - spacing;
      positionReason = "Above field - more space than below";
      // Ensure it doesn't go above screen
      if (calendarTop < margin) {
        calendarTop = margin;
        positionReason += " (adjusted to margin)";
      }
    }

    // Final boundary checks
    if (calendarTop < margin) {
      calendarTop = margin;
      positionReason += " (final: top margin)";
    } else if (calendarTop + calendarHeight > screenHeight - margin) {
      calendarTop = screenHeight - calendarHeight - margin;
      positionReason += " (final: bottom margin)";
    }

    return calendarTop;
  }

  double? _calculateCalendarLeft(BuildContext context) {
    if (fieldPosition.value == null) return 20;

    const calendarWidth = 450.0;
    final screenWidth = MediaQuery.of(context).size.width;
    const margin = 20.0;

    double left = fieldPosition.value!.dx;

    // For mobile screens, center the calendar
    if (screenWidth < 768) {
      left = (screenWidth - calendarWidth) / 2;
      if (left < margin) left = margin;
      if (left + calendarWidth > screenWidth - margin) {
        left = screenWidth - calendarWidth - margin;
      }
    } else {
      // For desktop, align with field but ensure it stays on screen
      // Ensure calendar doesn't go off screen right
      if (left + calendarWidth > screenWidth - margin) {
        left = screenWidth - calendarWidth - margin;
      }

      // Ensure calendar doesn't go off screen left
      if (left < margin) {
        left = margin;
      }
    }

    return left;
  }

  double? _calculateCalendarRight(BuildContext context) {
    // Return null to use left positioning instead
    return null;
  }

  Widget _formWidget(
    BuildContext context,
    bool isMobile,
    ValueNotifier<bool?> isBuyer,
    ValueNotifier<int> saleType,
    SignatureController _controller,
  ) {
    final size = MediaQuery.of(context).size;
    final isTab = Responsive.isTablet(context);
    return ValueListenableBuilder(
      valueListenable: selectedIndex,
      builder: (context, value, child) {
        return Container(
          width: double.infinity,
          decoration: BoxDecoration(
            image: DecorationImage(
              fit: BoxFit.cover,
              image: AssetImage('$imageAssetpath/register_bg.png'),
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                decoration: BoxDecoration(
                  color: AppTheme.roundIconColor,
                  borderRadius: BorderRadius.circular(25),
                ),
                margin: EdgeInsets.symmetric(
                  horizontal: isMobile
                      ? defaultPadding * 2.5
                      : isTab
                      ? defaultPadding * 3
                      : size.width / 4,
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Header with title and buttons
                    ValueListenableBuilder(
                      valueListenable: shouldShowEditButton,
                      builder: (context, editableValue, child) {
                        return _formHeader(context, isBuyer, isEditable);
                      },
                    ),

                    // Main form content
                    ValueListenableBuilder(
                      valueListenable: isDocAlreadySigned,
                      builder: (context, editableValue, child) {
                        return _formContent(
                          context,
                          isMobile,
                          isBuyer,
                          saleType,
                          _controller,
                        );
                      },
                    ),
                  ],
                ),
              ),
              SizedBox(
                height: selectedIndex.value == 0 ? defaultPadding * 2 : 0,
              ),
              // const Footer(),
            ],
          ),
        );
      },
    );
  }

  Container _formHeader(
    BuildContext context,
    ValueNotifier<bool?> isBuyer,
    ValueNotifier<bool> isEditable,
  ) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(
        horizontal: defaultPadding * 2,
        vertical: defaultPadding * 2,
      ),
      alignment: Alignment.center,
      child: Stack(
        alignment: Alignment.center,
        children: [
          SizedBox(
            width: double.infinity,
            child: Text(
              isEditable.value
                  ? isAgentEdit
                        ? AppStrings.reviewAndEditClosingDocumentData
                        : AppStrings.editClosingDocument
                  : AppStrings.closingDocument,
              textAlign: TextAlign.center,
              style: AppFonts.mediumTextStyle(28, color: Colors.white),
            ),
          ),
          if (shouldShowEditButton.value)
            Positioned(
              right: 0,
              child: AppButton(
                label: edit,

                backgroundColor: AppTheme.scaffoldBgColor,
                onPressed: () {
                  isEditable.value = true;
                  shouldShowEditButton.value = false;
                  _signatureController.clear();
                  signatureFile.value = null;
                  _backupFormState(context);
                },
                child: Row(
                  children: [
                    Image.asset(
                      '$iconAssetpath/edit.png',
                      height: 14,
                      width: 14,
                    ),
                    const SizedBox(width: 8),
                    Text(edit, style: AppFonts.mediumTextStyle(13)),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _formContent(
    BuildContext context,
    bool isMobile,
    ValueNotifier<bool?> isBuyer,
    ValueNotifier<int> saleType,
    SignatureController _controller,
  ) {
    final user = context.read<UserCubit>().state.user;
    return ValueListenableBuilder(
      valueListenable: closingDocFileName,
      builder: (context, value, child) {
        return Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(24),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.08),
                blurRadius: 24,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: LayoutBuilder(
            builder: (context, constraints) {
              return Form(
                key: _formKey,
                child: Column(
                  children: [
                    Padding(
                      padding: EdgeInsets.symmetric(vertical: defaultPadding),
                      child: Column(
                        children: [
                          _docRepresentingRadio(isBuyer, true, context),
                          const SizedBox(height: defaultPadding),
                          // Form fields in two columns
                          Column(
                            children: [
                              // Row 1
                              Container(
                                margin: const EdgeInsets.only(
                                  bottom: defaultPadding,
                                ),
                                padding: _formContainerPadding(),
                                decoration: BoxDecoration(
                                  color: AppTheme.formSubsectionBgColor,
                                  // borderRadius: BorderRadius.circular(12),
                                ),
                                child: isMobile
                                    ? _transactionInfoColumn(context)
                                    : _transactionInfoRow(
                                        context,
                                        controllerLeft: transactionIdController,
                                        fieldLeft: transactionId,
                                        iconLeft: "form_transaction_id.png",
                                        valueLeft: '',
                                        keyboardTypeLeft: null,
                                        enableLeft: true,
                                        controllerRight:
                                            transactionNameController,
                                        fieldRight: transactionName,
                                        iconRight: 'represented_contact.png',
                                        valueRight: '',
                                        keyboardTypeRight: TextInputType.text,
                                        enableRight: false,
                                      ),
                              ),
                              // Row 2
                              Container(
                                margin: const EdgeInsets.only(
                                  bottom: defaultPadding,
                                ),
                                padding: _formContainerPadding(),
                                child: isMobile
                                    ? Column(
                                        children: [
                                          buildTextField(
                                            addressController,
                                            address,
                                            'form_address_book.png',
                                            "",
                                            isMandatory: true,
                                          ),
                                          buildTextField(
                                            salesVolumeController,
                                            salesVolume,
                                            'dollar.png',
                                            "",
                                            isMandatory: true,
                                            keyboardType:
                                                TextInputType.numberWithOptions(
                                                  decimal: true,
                                                ),
                                            inputFormatters: [
                                              CurrencyInputFormatter(
                                                allowDecimal: true,
                                              ),
                                            ],
                                          ),
                                        ],
                                      )
                                    : _transactionInfoRow(
                                        context,
                                        controllerLeft: addressController,
                                        fieldLeft: address,
                                        iconLeft: 'form_address_book.png',
                                        valueLeft: '',
                                        keyboardTypeLeft: null,
                                        enableLeft: true,
                                        controllerRight: salesVolumeController,
                                        fieldRight: salesVolume,
                                        iconRight: 'dollar.png',
                                        valueRight: '',
                                        keyboardTypeRight:
                                            TextInputType.numberWithOptions(
                                              decimal: true,
                                            ),
                                        enableRight: true,
                                      ),
                              ),
                              // Row 3
                              Container(
                                margin: const EdgeInsets.only(
                                  bottom: defaultPadding,
                                ),
                                padding: _formContainerPadding(),
                                decoration: BoxDecoration(
                                  color: AppTheme.formSubsectionBgColor,
                                  // borderRadius: BorderRadius.circular(12),
                                ),
                                child: isMobile
                                    ? Column(
                                        children: [
                                          buildTextField(
                                            moneyReceivedController,
                                            moneyReceived,
                                            'dollar.png',
                                            "",
                                            isMandatory: true,
                                            keyboardType:
                                                TextInputType.numberWithOptions(
                                                  decimal: true,
                                                ),
                                            inputFormatters: [
                                              CurrencyInputFormatter(
                                                allowDecimal: true,
                                              ),
                                            ],
                                          ),
                                          buildTextField(
                                            dateDepositedController,
                                            dateDeposited,
                                            'form_calendar.png',
                                            "",
                                            isMandatory: true,
                                          ),
                                        ],
                                      )
                                    : _transactionInfoRow(
                                        context,
                                        controllerLeft: moneyReceivedController,
                                        fieldLeft: moneyReceived,
                                        iconLeft: 'dollar.png',
                                        valueLeft: '',
                                        keyboardTypeLeft:
                                            TextInputType.numberWithOptions(
                                              decimal: true,
                                            ),

                                        enableLeft: null,
                                        controllerRight:
                                            dateDepositedController,
                                        fieldRight: dateDeposited,
                                        iconRight: 'form_calendar.png',
                                        valueRight: '',
                                        keyboardTypeRight: TextInputType.number,
                                        enableRight: null,
                                      ),
                              ),
                              // Sale Type
                              _saleTypeSelection(isMobile, true, context),
                              SizedBox(height: defaultPadding),
                              // Row 4
                              Container(
                                padding: _formContainerPadding(),
                                decoration: BoxDecoration(
                                  color: AppTheme.formSubsectionBgColor,
                                  // borderRadius: BorderRadius.circular(12),
                                ),
                                child: isMobile
                                    ? Column(
                                        children: [
                                          buildTextField(
                                            grossCommissionController,
                                            grossCommission,
                                            'dollar.png',
                                            "",
                                            isMandatory: true,
                                            keyboardType:
                                                TextInputType.numberWithOptions(
                                                  decimal: true,
                                                ),
                                            inputFormatters: [
                                              CurrencyInputFormatter(
                                                allowDecimal: true,
                                              ),
                                            ],
                                          ),
                                          const SizedBox(
                                            height: defaultPadding,
                                          ),
                                          buildTextField(
                                            commissionSplitController,
                                            commissionSplit,
                                            'form_percentage.png',
                                            "",
                                            isMandatory: true,
                                            enabled: true,
                                            keyboardType:
                                                TextInputType.numberWithOptions(
                                                  decimal: true,
                                                ),
                                          ),
                                        ],
                                      )
                                    : _transactionInfoRow(
                                        context,
                                        controllerLeft:
                                            grossCommissionController,
                                        fieldLeft: grossCommission,
                                        iconLeft: 'dollar.png',
                                        valueLeft: '',
                                        keyboardTypeLeft:
                                            TextInputType.numberWithOptions(
                                              decimal: true,
                                            ),
                                        enableLeft: null,
                                        controllerRight:
                                            commissionSplitController,
                                        fieldRight: commissionSplit,
                                        iconRight: 'form_percentage.png',
                                        valueRight: '',
                                        keyboardTypeRight:
                                            TextInputType.numberWithOptions(
                                              decimal: true,
                                            ),
                                        enableRight: true,
                                      ),
                              ),
                              SizedBox(height: defaultPadding),
                              // Row 5
                              Container(
                                padding: _formContainerPadding(),
                                child: isMobile
                                    ? Column(
                                        children: [
                                          buildTextField(
                                            dateReleasedController,
                                            dateReleased,
                                            'form_calendar.png',
                                            "",
                                            isMandatory: true,
                                          ),
                                          const SizedBox(
                                            height: defaultPadding,
                                          ),
                                          buildTextField(
                                            listingDateController,
                                            listingDate,
                                            'form_calendar.png',
                                            "",
                                            isMandatory: true,
                                          ),
                                        ],
                                      )
                                    : _transactionInfoRow(
                                        context,
                                        controllerLeft: dateReleasedController,
                                        fieldLeft: dateReleased,
                                        iconLeft: 'form_calendar.png',
                                        valueLeft: '',
                                        keyboardTypeLeft: null,
                                        enableLeft: null,
                                        controllerRight: listingDateController,
                                        fieldRight: listingDate,
                                        iconRight: 'form_calendar.png',
                                        valueRight: '',
                                        keyboardTypeRight: TextInputType.number,
                                        enableRight: null,
                                      ),
                              ),
                              // Row 6
                              SizedBox(height: defaultPadding),
                              Container(
                                padding: _formContainerPadding(),
                                decoration: BoxDecoration(
                                  color: AppTheme.formSubsectionBgColor,
                                  // borderRadius: BorderRadius.circular(12),
                                ),
                                child: isMobile
                                    ? Column(
                                        children: [
                                          buildTextField(
                                            expirationDateController,
                                            AppStrings.expirationDate,
                                            'form_calendar.png',
                                            "",
                                            isMandatory: true,
                                          ),
                                          const SizedBox(
                                            height: defaultPadding,
                                          ),
                                          buildTextField(
                                            closingDateController,
                                            closingDate,
                                            'form_calendar.png',
                                            "",
                                            isMandatory: true,
                                          ),
                                        ],
                                      )
                                    : _transactionInfoRow(
                                        context,
                                        controllerLeft:
                                            expirationDateController,
                                        fieldLeft: AppStrings.expirationDate,
                                        iconLeft: 'form_calendar.png',
                                        valueLeft: '',
                                        keyboardTypeLeft: null,
                                        enableLeft: null,
                                        controllerRight: closingDateController,
                                        fieldRight: closingDate,
                                        iconRight: 'form_calendar.png',
                                        valueRight: '',
                                        keyboardTypeRight: TextInputType.number,
                                        enableRight: null,
                                      ),
                              ),
                              SizedBox(height: defaultPadding),
                              // Row 7
                              Container(
                                padding: _formContainerPadding(),
                                child: isMobile
                                    ? Column(
                                        children: [
                                          buildTextField(
                                            legalDescriptionController,
                                            legalDescription,
                                            'legal_desc.png',
                                            "",
                                            isMandatory: true,
                                          ),
                                          const SizedBox(
                                            height: defaultPadding,
                                          ),
                                          buildTextField(
                                            escrowNumberController,
                                            escrowNumber,
                                            'form_escrow_no.png',
                                            "",
                                            isMandatory: true,
                                          ),
                                        ],
                                      )
                                    : _transactionInfoRow(
                                        context,
                                        controllerLeft:
                                            legalDescriptionController,
                                        fieldLeft: legalDescription,
                                        iconLeft: 'legal_desc.png',
                                        valueLeft: '',
                                        keyboardTypeLeft: null,
                                        enableLeft: null,
                                        controllerRight: escrowNumberController,
                                        fieldRight: escrowNumber,
                                        iconRight: 'form_escrow_no.png',
                                        valueRight: '',
                                        keyboardTypeRight: TextInputType.text,
                                        enableRight: null,
                                      ),
                              ),
                              SizedBox(height: defaultPadding),
                              // Row 8
                              Container(
                                margin: const EdgeInsets.only(
                                  bottom: defaultPadding,
                                ),
                                padding: _formContainerPadding(),
                                decoration: BoxDecoration(
                                  color: AppTheme.formSubsectionBgColor,
                                  // borderRadius: BorderRadius.circular(12),
                                ),
                                child: isMobile
                                    ? Column(
                                        children: [
                                          buildTextField(
                                            dateReceivedController,
                                            dateReceived,
                                            'form_calendar.png',
                                            "",
                                            isMandatory: true,
                                          ),
                                          const SizedBox(
                                            height: defaultPadding,
                                          ),
                                          buildTextField(
                                            amountReleasedController,
                                            amountReleased,
                                            'dollar.png',
                                            "",
                                            isMandatory: true,
                                            keyboardType:
                                                TextInputType.numberWithOptions(
                                                  decimal: true,
                                                ),
                                            inputFormatters: [
                                              CurrencyInputFormatter(
                                                allowDecimal: true,
                                              ),
                                            ],
                                          ),
                                        ],
                                      )
                                    : _transactionInfoRow(
                                        context,
                                        controllerLeft: dateReceivedController,
                                        fieldLeft: dateReceived,
                                        iconLeft: 'form_calendar.png',
                                        valueLeft: '',
                                        keyboardTypeLeft: null,
                                        enableLeft: null,
                                        controllerRight:
                                            amountReleasedController,
                                        fieldRight: amountReleased,
                                        iconRight: 'dollar.png',
                                        valueRight: '',
                                        keyboardTypeRight:
                                            TextInputType.numberWithOptions(
                                              decimal: true,
                                            ),
                                        enableRight: null,
                                      ),
                              ),
                              // Row 9
                              Align(
                                alignment: Alignment.centerLeft,
                                child: Container(
                                  width: isMobile
                                      ? double.infinity
                                      : (constraints.maxWidth -
                                                (defaultPadding * 5)) /
                                            2,
                                  margin: const EdgeInsets.only(
                                    bottom: defaultPadding,
                                    left: defaultPadding * 2,
                                  ),
                                  padding: const EdgeInsets.symmetric(
                                    vertical: 12,
                                  ),
                                  child: buildTextField(
                                    representedContactController,
                                    representedContact,
                                    'represented_contact.png',
                                    "",
                                    isMandatory: true,
                                  ),
                                ),
                              ),
                              // Row 10
                              Container(
                                margin: const EdgeInsets.only(
                                  bottom: defaultPadding,
                                ),
                                padding: _formContainerPadding(),
                                decoration: BoxDecoration(
                                  color: AppTheme.formSubsectionBgColor,
                                  // borderRadius: BorderRadius.circular(12),
                                ),
                                child: isMobile
                                    ? Column(
                                        children: [
                                          buildTextField(
                                            firstNameController,
                                            firstName,
                                            'form_user.png',
                                            "",
                                            isMandatory: true,
                                          ),

                                          const SizedBox(
                                            height: defaultPadding,
                                          ),
                                          buildTextField(
                                            lastNameController,
                                            lastName,
                                            'form_user.png',
                                            "",
                                            isMandatory: true,
                                          ),
                                        ],
                                      )
                                    : _transactionInfoRow(
                                        context,
                                        controllerLeft: firstNameController,
                                        fieldLeft: firstName,
                                        iconLeft: 'form_user.png',
                                        valueLeft: '',
                                        keyboardTypeLeft: null,
                                        enableLeft: null,
                                        controllerRight: lastNameController,
                                        fieldRight: lastName,
                                        iconRight: 'form_user.png',
                                        valueRight: '',
                                        keyboardTypeRight: TextInputType.text,
                                        enableRight: null,
                                      ),
                              ),
                              // Row 11
                              Container(
                                margin: const EdgeInsets.only(
                                  bottom: defaultPadding,
                                ),
                                padding: _formContainerPadding(),
                                child: isMobile
                                    ? Column(
                                        children: [
                                          buildTextField(
                                            emailController,
                                            email,
                                            'mail.png',
                                            "",
                                            isMandatory: true,
                                          ),
                                          const SizedBox(
                                            height: defaultPadding,
                                          ),

                                          buildTextField(
                                            companyPhoneNumberController,
                                            companyPhoneNumber,
                                            'form_phone.png',
                                            "",
                                            inputFormatters: [
                                              FilteringTextInputFormatter
                                                  .digitsOnly,
                                              LengthLimitingTextInputFormatter(
                                                10,
                                              ),
                                            ],
                                            isMandatory: true,
                                          ),
                                        ],
                                      )
                                    : _transactionInfoRow(
                                        context,
                                        controllerLeft: emailController,
                                        fieldLeft: email,
                                        iconLeft: 'mail.png',
                                        valueLeft: '',
                                        keyboardTypeLeft: null,
                                        enableLeft: null,
                                        controllerRight:
                                            companyPhoneNumberController,
                                        fieldRight: companyPhoneNumber,
                                        iconRight: 'form_phone.png',
                                        valueRight: '',
                                        keyboardTypeRight: TextInputType.number,
                                        enableRight: null,
                                      ),
                              ),
                              // Row 12
                              Container(
                                margin: const EdgeInsets.only(
                                  bottom: defaultPadding,
                                ),
                                padding: _formContainerPadding(),
                                decoration: BoxDecoration(
                                  color: AppTheme.formSubsectionBgColor,
                                  // borderRadius: BorderRadius.circular(12),
                                ),
                                child: isMobile
                                    ? Column(
                                        children: [
                                          buildTextField(
                                            contactAddressController,
                                            contactAddress,
                                            'form_address_book.png',
                                            "",
                                            isMandatory: true,
                                          ),
                                          const SizedBox(
                                            height: defaultPadding,
                                          ),
                                          buildTextField(
                                            leadSourceController,
                                            leadSource,
                                            'referral.png',
                                            "",
                                            context: context,
                                            isMandatory: true,
                                          ),
                                        ],
                                      )
                                    : _transactionInfoRow(
                                        context,
                                        controllerLeft:
                                            contactAddressController,
                                        fieldLeft: contactAddress,
                                        iconLeft: 'form_address_book.png',
                                        valueLeft: '',
                                        keyboardTypeLeft: null,
                                        enableLeft: null,
                                        controllerRight: leadSourceController,
                                        fieldRight: leadSource,
                                        iconRight: 'referral.png',
                                        valueRight: '',
                                        keyboardTypeRight: TextInputType.text,
                                        enableRight: null,
                                      ),
                              ),
                              // Row 13
                              ValueListenableBuilder(
                                valueListenable: isDocAlreadySigned,
                                builder: (context, value, child) {
                                  return closingDocFileName.value.isNotEmpty
                                      ? Container(
                                          margin: const EdgeInsets.only(
                                            bottom: defaultPadding,
                                          ),
                                          padding: _formContainerPadding(),
                                          child: isMobile
                                              ? Column(
                                                  children: [
                                                    buildTextFieldWithButtons(
                                                      isDocAlreadySigned
                                                                  .value &&
                                                              user?.role ==
                                                                  UserRole
                                                                      .brokerage
                                                          ? AppStrings
                                                                .signedClosingDocument
                                                          : AppStrings
                                                                .closingDocument,
                                                      'doc.png',
                                                      closingDocFileName.value,
                                                      isMandatory: true,
                                                    ),
                                                  ],
                                                )
                                              : _documentRowWidget(context),
                                        )
                                      : Container();
                                },
                              ),
                            ],
                          ),

                          // Signature and Upload with 'or' text and dotted border for upload
                          if (isEditable.value) ...[
                            if (!isAgentEdit) ...[
                              _signatureWidget(_controller),
                              SizedBox(height: defaultPadding * 3),
                            ],
                            // Buttons
                            _formFooterActionBtns(isBuyer, context),
                            const SizedBox(height: defaultPadding),
                          ],
                        ],
                      ),
                    ),
                    // Footer
                  ],
                ),
              );
            },
          ),
        );
      },
    );
  }

  EdgeInsets _formContainerPadding() {
    return const EdgeInsets.symmetric(
      vertical: defaultPadding / 2,
      horizontal: defaultPadding * 1.5,
    );
  }

  Widget _documentRowWidget(BuildContext context) {
    final user = context.read<UserCubit>().state.user;
    return ValueListenableBuilder(
      valueListenable: isDocAlreadySigned,
      builder: (context, value, child) {
        return Row(
          children: [
            // TODO: Uncomment sales agreement in future if needed
            // Expanded(
            //   child: buildTextFieldWithButtons(
            //     AppStrings.salesAgreement,
            //     'doc.png',
            //     '',
            //   ),
            // ),
            // const SizedBox(width: 24),
            Expanded(
              child: buildTextFieldWithButtons(
                isDocAlreadySigned.value && user?.role == UserRole.brokerage
                    ? AppStrings.signedClosingDocument
                    : AppStrings.closingDocument,
                'doc.png',
                closingDocFileName.value,
                isMandatory: true,
              ),
            ),
            const SizedBox(width: 24), // Add spacing between items
            Expanded(
              flex: 1, // Second half - empty to maintain layout
              child: Container(), // Empty container to maintain layout
            ),
          ],
        );
      },
    );
  }

  Widget _saleTypeSelection(
    bool isMobile,
    bool isMandatory,
    BuildContext context,
  ) {
    final saleDocCubit = context.read<SalesDetailsCubit>();
    final saleTypes = saleDocCubit.transactionTypeData;

    return ValueListenableBuilder<TransactionType?>(
      valueListenable: selectedTransactionType, //  use the actual notifier
      builder: (context, selectedItem, _) {
        final radioWidgets = <Widget>[
          RichText(
            text: TextSpan(
              text: '${AppStrings.saleTypeLabel}:',
              style: AppFonts.regularTextStyle(
                14,
                color: AppTheme.primaryTextColor,
              ),
              children: isMandatory && isEditable.value
                  ? [
                      TextSpan(
                        text: ' *',
                        style: AppFonts.regularTextStyle(
                          14,
                          color: AppTheme.textFieldMandatoryColor,
                        ),
                      ),
                    ]
                  : [],
            ),
          ),
          ...saleTypes.map((item) {
            return RadioOption<String>(
              value: item?.id ?? '',
              groupValue:
                  selectedItem?.id ?? '', //  compare with selected model
              label: item?.value ?? '',
              onChanged: (value) {
                if (item != null) {
                  context.read<SalesDetailsCubit>().updateTransactionType(item);
                  selectedTransactionType.value = item; //  update actual model
                  _backupFormState(context);
                }
              },
              enabled: isEditable.value,
            );
          }).toList(),
        ];

        return Container(
          padding: const EdgeInsets.symmetric(horizontal: defaultPadding),
          child: isMobile
              ? Wrap(
                  spacing: defaultPadding,
                  runSpacing: defaultPadding,
                  children: radioWidgets,
                )
              : Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: radioWidgets,
                ),
        );
      },
    );
  }

  Widget _transactionInfoRow(
    BuildContext? context, {
    required TextEditingController controllerLeft,
    required String fieldLeft,
    required String iconLeft,
    required String valueLeft,
    required TextInputType? keyboardTypeLeft,
    required bool? enableLeft,
    required TextEditingController controllerRight,
    required String fieldRight,
    required String iconRight,
    required String valueRight,
    required TextInputType? keyboardTypeRight,
    required bool? enableRight,
  }) {
    return Row(
      children: [
        Expanded(
          child: buildTextField(
            controllerLeft,
            fieldLeft,
            iconLeft,
            valueLeft,
            keyboardType: keyboardTypeLeft,
            context: context,
            isMandatory: true,
            inputFormatters: _resolveInputFormatters(
              keyboardType: keyboardTypeLeft,
              field: fieldLeft,
            ),
            enabled: enableLeft ?? true,
          ),
        ),
        const SizedBox(width: 24),
        Expanded(
          child: buildTextField(
            controllerRight,
            fieldRight,
            iconRight,
            valueRight,
            keyboardType: keyboardTypeRight,
            context: context,
            isMandatory: true,
            inputFormatters: _resolveInputFormatters(
              keyboardType: keyboardTypeRight,
              field: fieldRight,
            ),
            enabled: enableRight ?? true,
          ),
        ),
      ],
    );
  }

  /// 🔹 Centralized formatter resolver
  List<TextInputFormatter> _resolveInputFormatters({
    required TextInputType? keyboardType,
    required String field,
  }) {
    if (keyboardType == TextInputType.number) {
      // Digits only
      return [
        FilteringTextInputFormatter.digitsOnly,
        if (field == companyPhoneNumber) LengthLimitingTextInputFormatter(10),
      ];
    }

    if (field == commissionSplit) {
      return [CommissionSplitFormatter()];
    }

    if (keyboardType == const TextInputType.numberWithOptions(decimal: true)) {
      return [CurrencyInputFormatter(allowDecimal: true)];
    }

    if (keyboardType == const TextInputType.numberWithOptions(decimal: false)) {
      return [CurrencyInputFormatter(allowDecimal: false)];
    }

    return [];
  }

  Widget _transactionInfoColumn(BuildContext context) {
    return Column(
      children: [
        buildTextField(
          transactionIdController,
          transactionId,
          "form_transaction_id.png",
          "",
          context: context,
          isMandatory: true,
        ),
        buildTextField(
          transactionNameController,
          transactionName,
          'represented_contact.png',
          "",
          context: context,
          isMandatory: true,
          enabled: false,
        ),
      ],
    );
  }

  Future<void> fetchSalesClosingDocumentDetails(BuildContext context) async {
    // Fetch sales closing document details from the API or data source
    {
      final user = context.read<UserCubit>().state.user;
      final saleDocCubit = context.read<SalesDetailsCubit>();
      await saleDocCubit.fetchSalesClosingDocumentDetails(
        salesID,
        user?.userId ?? '',
      );
      final state = saleDocCubit.state;
      if (state is SalesDetailsClosingDocumentLoaded) {
        final salesClosingDocDetailsApi = state.salesClosingDocDetailsApi;
        final representingvalue =
            salesClosingDocDetailsApi?.saleData?.representing;
        if (representingvalue != null && representingvalue.isNotEmpty) {
          final repStr =
              salesClosingDocDetailsApi?.saleData?.representing ?? '';
          if (repStr.isNotEmpty &&
              saleDocCubit.representingTypesData.isNotEmpty) {
            final matchRep = saleDocCubit.representingTypesData.firstWhere(
              (item) => item?.value == repStr,
            );
            representingTypeString.value = matchRep?.value;
            selectedRepresentingType.value = matchRep;
            if (matchRep != null) {
              context.read<SalesDetailsCubit>().updateRepresentingType(
                selectedRepresentingType.value,
              );
            }
          }
        } else {
          representingTypeString.value = '';
        }
        final reviewedStatus =
            salesClosingDocDetailsApi?.saleData?.reviewedStatus;
        final isBrokerage = user?.role == UserRole.brokerage;

        if (reviewedStatus == AppStrings.brokerageReviewed) {
          // Brokerage already reviewed → disable edit
          shouldShowEditButton.value = false;
          isDocAlreadySigned.value = true;
          FormStateManager().saveState({
            'isDocAlreadySigned': 'true',
            'isEditable': 'false',
          });
        } else if (reviewedStatus == AppStrings.agentReviewed) {
          if (isBrokerage) {
            // Brokerage role and not yet fully reviewed → allow edit
            shouldShowEditButton.value = true;
            isDocAlreadySigned.value = false;
            FormStateManager().saveState({
              'isDocAlreadySigned': 'false',
              'isEditable': 'true',
            });
          } else {
            // Agent role and reviewed by agent → disable edit
            shouldShowEditButton.value = false;
            isDocAlreadySigned.value = true;
            FormStateManager().saveState({
              'isDocAlreadySigned': 'true',
              'isEditable': 'false',
            });
          }
        } else {
          if (user?.role == UserRole.brokerage ||
              user?.role == UserRole.agent) {
            shouldShowEditButton.value = user?.role == UserRole.brokerage;
            isDocAlreadySigned.value = false;
            FormStateManager().saveState({
              'isDocAlreadySigned': 'false',
              'isEditable': 'true',
            });
          } else {
            shouldShowEditButton.value = false;
            isDocAlreadySigned.value = true;
            FormStateManager().saveState({
              'isDocAlreadySigned': 'true',
              'isEditable': 'false',
            });
          }
        }
        FormStateManager().saveState({
          'shouldShowEditButton': shouldShowEditButton.value.toString(),
        });
        salesAgentId = salesClosingDocDetailsApi?.saleData?.agentId ?? '';
        propertyType = salesClosingDocDetailsApi?.saleData?.propertyType ?? '';
        propertyDescription =
            salesClosingDocDetailsApi?.saleData?.propertyDescription ?? '';
        lenderInfo = salesClosingDocDetailsApi?.saleData?.lenderInfo ?? '';
        escrowCompany =
            salesClosingDocDetailsApi?.saleData?.escrowCompany ?? '';
        transactionIDString.value =
            salesClosingDocDetailsApi?.saleData?.transactionType ?? '';
        final transType =
            salesClosingDocDetailsApi?.saleData?.transactionType ?? '';
        if (transType.isNotEmpty &&
            saleDocCubit.transactionTypeData.isNotEmpty) {
          final matchTrans = saleDocCubit.transactionTypeData.firstWhere(
            (item) =>
                item?.id ==
                salesClosingDocDetailsApi?.saleData?.transactionType,
          );
          transactionTypeString.value = matchTrans?.value;
          selectedTransactionType.value = matchTrans;
          if (matchTrans != null) {
            context.read<SalesDetailsCubit>().updateTransactionType(
              selectedTransactionType.value,
            );
          }
        }
        transactionIdController.text =
            salesClosingDocDetailsApi?.saleData?.transactionFileNumber ?? "";
        transactionNameController.text =
            salesClosingDocDetailsApi?.saleData?.transactionName ?? "";
        addressController.text =
            salesClosingDocDetailsApi?.saleData?.propertyAddress ?? "";
        salesVolumeController.text =
            salesClosingDocDetailsApi?.saleData?.salesVolume != null
            ? salesClosingDocDetailsApi?.saleData?.salesVolume.toString() ?? ""
            : "";
        moneyReceivedController.text =
            salesClosingDocDetailsApi?.saleData?.depositReceived != null
            ? salesClosingDocDetailsApi?.saleData?.depositReceived.toString() ??
                  ""
            : "";
        if (salesClosingDocDetailsApi?.saleData?.dateDeposited != null) {
          dateDepositedController.text = AppDateFormatter.formatDateMMddyyyy(
            salesClosingDocDetailsApi?.saleData?.dateDeposited,
          );
        }
        grossCommissionController.text =
            salesClosingDocDetailsApi?.saleData?.grossCommission != null
            ? salesClosingDocDetailsApi?.saleData?.grossCommission.toString() ??
                  ""
            : "";
        commissionSplitController.text =
            salesClosingDocDetailsApi?.saleData?.commissionSplit != null
            ? salesClosingDocDetailsApi?.saleData?.commissionSplit.toString() ??
                  ""
            : "";
        if (salesClosingDocDetailsApi?.saleData?.dateReleased != null) {
          dateReleasedController.text = AppDateFormatter.formatDateMMddyyyy(
            salesClosingDocDetailsApi?.saleData?.dateReleased,
          );
        }
        if (salesClosingDocDetailsApi?.saleData?.listingDate != null) {
          listingDateController.text = AppDateFormatter.formatDateMMddyyyy(
            salesClosingDocDetailsApi?.saleData?.listingDate,
          );
        }
        if (salesClosingDocDetailsApi?.saleData?.expirationDate != null) {
          expirationDateController.text = AppDateFormatter.formatDateMMddyyyy(
            salesClosingDocDetailsApi?.saleData?.expirationDate,
          );
        }
        if (salesClosingDocDetailsApi?.saleData?.closingDate != null) {
          closingDateController.text = AppDateFormatter.formatDateMMddyyyy(
            salesClosingDocDetailsApi?.saleData?.closingDate,
          );
        }
        legalDescriptionController.text =
            salesClosingDocDetailsApi?.saleData?.legalDescription ?? "";
        escrowNumberController.text =
            salesClosingDocDetailsApi?.saleData?.escrowNumber ?? "";
        if (salesClosingDocDetailsApi?.saleData?.dateReceived != null) {
          dateReceivedController.text = AppDateFormatter.formatDateMMddyyyy(
            salesClosingDocDetailsApi?.saleData?.dateReceived,
          );
        }
        amountReleasedController.text =
            salesClosingDocDetailsApi?.saleData?.amountReleased != null
            ? salesClosingDocDetailsApi?.saleData?.amountReleased.toString() ??
                  ""
            : "";
        final firstname =
            salesClosingDocDetailsApi?.saleData?.representingFirstName ?? "";
        final lastName =
            salesClosingDocDetailsApi?.saleData?.representingLastName ?? "";
        representedContactController.text =
            firstname + (lastName.isNotEmpty ? ' $lastName' : '');
        firstNameController.text =
            salesClosingDocDetailsApi?.saleData?.representingFirstName ?? "";
        lastNameController.text =
            salesClosingDocDetailsApi?.saleData?.representingLastName ?? "";
        emailController.text =
            salesClosingDocDetailsApi?.saleData?.representingEmail ?? "";
        companyPhoneNumberController.text =
            salesClosingDocDetailsApi
                ?.saleData
                ?.representingCompanyPhoneNumber ??
            "";
        contactAddressController.text =
            salesClosingDocDetailsApi?.saleData?.representingAddress ?? "";
        final leadSource = salesClosingDocDetailsApi?.saleData?.leadSource;
        if (leadSource != null && leadSource.isNotEmpty) {
          final matchLeadSource = saleDocCubit.leadSourceData.firstWhere(
            (item) => item?.id == leadSource,
          );
          if (matchLeadSource != null) {
            leadSourceController.text = matchLeadSource.value ?? '';
            selectedLeadSourceType.value = matchLeadSource;
            context.read<SalesDetailsCubit>().updateLeadSource(
              selectedLeadSourceType.value,
            );
          }
        } else {
          leadSourceController.text = '';
        }

        closingDocFileName.value =
            salesClosingDocDetailsApi?.fileDetails?.fileName ?? '';
        closingDocFileUrl =
            salesClosingDocDetailsApi?.fileDetails?.fileUrl ?? '';
        final fileName = salesClosingDocDetailsApi?.fileDetails?.fileName;
        final fileUrl = salesClosingDocDetailsApi?.fileDetails?.fileUrl;

        context.read<SalesDetailsCubit>().updateClosingDocFileName(fileName);
        context.read<SalesDetailsCubit>().updateClosingDocFileUrl(fileUrl);
        FormStateManager().saveState({
          'closingDocFileName':
              salesClosingDocDetailsApi?.fileDetails?.fileName ?? '',
          'closingDocFileUrl':
              salesClosingDocDetailsApi?.fileDetails?.fileUrl ?? '',
        });
        _backupFormState(context);
      } else if (state is SalesDetailsError) {
        debugPrint(
          'Error fetching sales closing doc details: ${state.message}',
        );
      } else {
        debugPrint('Unknown state: $state');
      }
    }
  }

  Widget _formFooterActionBtns(
    ValueNotifier<bool?> isBuyer,
    BuildContext context,
  ) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        ElevatedButton(
          onPressed: () async {
            if (isAgentEdit) {
              _backupFormState(context);
              await showDialog<bool>(
                context: context,
                builder: (ctx) => showAlertDialogue(
                  ctx,
                  title: reviewDocCancelTitle,
                  content: reviewDocCancelContent,
                  titlePadding: 1.5,
                  positiveButtonText: yes,
                  negativeButtonText: no,
                  onPositivePressed: () async {
                    Navigator.of(ctx).pop();
                    await fetchSalesClosingDocumentDetails(context);
                    _reset(context, isBuyer);
                    _navigateToSales(context);
                  },
                ),
              );
            } else {
              fetchSalesClosingDocumentDetails(context);
              _reset(context, isBuyer);
            }
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.cancelBgColor,
            minimumSize: const Size(120, 44),
          ),
          child: Text(
            AppStrings.cancel,
            style: TextStyle(color: Colors.black87),
          ),
        ),
        const SizedBox(width: 24),
        ElevatedButton(
          onPressed: () => isAgentEdit
              ? _validateAgentSubmission(isBuyer, context, _signatureController)
              : _validateAndShowPdfPreview(isBuyer, context),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.primaryBlueColor,
            minimumSize: const Size(120, 44),
          ),
          child: Text(
            isAgentEdit ? AppStrings.submit : AppStrings.pdfPreview,
            style: TextStyle(color: Colors.white),
          ),
        ),
      ],
    );
  }

  void _validateAgentSubmission(
    ValueNotifier<bool?> isBuyer,
    BuildContext context,
    SignatureController controller,
  ) {
    // Trigger validation
    _backupFormState(context);
    if (_formKey.currentState?.validate() ?? false) {
      if (selectedRepresentingType.value == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppStrings.pleaseSelectRepresentingValues),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 3),
          ),
        );
      } else if (selectedTransactionType.value == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppStrings.pleaseSelectSaleTypeValues),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 3),
          ),
        );
      } else {
        _submitForm(isBuyer, context, controller);
      }
    } else {
      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppStrings.pleaseFillRequiredFields),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 3),
        ),
      );
    }
  }

  void _validateAndShowPdfPreview(
    ValueNotifier<bool?> isBuyer,
    BuildContext context,
  ) async {
    // Trigger validation and backup form state
    _backupFormState(context);

    // Check form validation first
    final isFormValid = _formKey.currentState?.validate() ?? false;

    if (!isFormValid) {
      // Show error message for form validation and return early
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppStrings.pleaseFillRequiredFields),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 3),
        ),
      );
      return; // Exit early to show validation errors
    }

    // Check representing selection
    if (selectedRepresentingType.value == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppStrings.pleaseSelectRepresentingValues),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 3),
        ),
      );
      return;
    }

    // Check transaction type selection
    if (selectedTransactionType.value == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppStrings.pleaseSelectSaleTypeValues),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 3),
        ),
      );
      return;
    }

    // Check signature validation
    final hasUploadedSignature = signatureFile.value != null;
    final hasDrawnSignature = _signatureController.isNotEmpty;

    if (!hasUploadedSignature && !hasDrawnSignature) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppStrings.pleaseFillRequiredFields),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 3),
        ),
      );
      return;
    }

    // All validations passed, proceed with PDF preview
    if (hasUploadedSignature && hasDrawnSignature) {
      await _showSignatureSelectionDialog(context, isBuyer);
    } else {
      // If only one signature is available, use that
      _selectedSignatureSource = hasUploadedSignature
          ? SignatureSource.uploaded
          : SignatureSource.drawn;
      _showPdfPreview(isBuyer, context, _signatureController);
    }
  }

  Widget _buildWebPdfPreview(Uint8List pdfBytes) {
    final base64String = base64Encode(pdfBytes);
    final dataUrl = 'data:application/pdf;base64,$base64String';

    // Create a unique view type for this PDF preview
    final viewType =
        '${AppStrings.pdfPreviewFileName}${DateTime.now().millisecondsSinceEpoch}';

    // Register the view factory
    ui_web.platformViewRegistry.registerViewFactory(viewType, (int viewId) {
      final iframe = html.IFrameElement()
        ..src = dataUrl
        ..style.border = 'none'
        ..style.width = '100%'
        ..style.height = '100%';
      return iframe;
    });

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: HtmlElementView(viewType: viewType),
      ),
    );
  }

  Widget _buildMobilePdfPreview(Uint8List pdfBytes) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.picture_as_pdf, size: 64, color: Colors.red),
            SizedBox(height: 16),
            Text(AppStrings.pdfPreview, style: AppFonts.mediumTextStyle(18)),
            SizedBox(height: 8),
            Text(
              AppStrings.salesReviewDocument,
              style: AppFonts.regularTextStyle(14, color: Colors.grey),
            ),
            SizedBox(height: 8),
            Text(
              '${(pdfBytes.length / 1024).toStringAsFixed(1)} KB',
              style: AppFonts.regularTextStyle(12, color: Colors.grey),
            ),
            SizedBox(height: 16),
            Text(
              AppStrings.mobilePreviewNotAvailable,
              textAlign: TextAlign.center,
              style: AppFonts.regularTextStyle(12, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }

  List<pw.TableRow> _buildPdfPreviewItems() {
    return [
      _buildCompactPdfTableRow(
        AppStrings.transactionId,
        transactionIdController.text,
      ),
      _buildCompactPdfTableRow(
        AppStrings.transactionName,
        transactionNameController.text,
      ),
      _buildCompactPdfTableRow(AppStrings.address, addressController.text),
      _buildCompactPdfTableRow(
        AppStrings.salesVolume,
        salesVolumeController.text,
      ),
      _buildCompactPdfTableRow(
        AppStrings.moneyReceived,
        moneyReceivedController.text,
      ),
      _buildCompactPdfTableRow(
        AppStrings.dateDeposited,
        dateDepositedController.text,
      ),
      _buildSinglePdfTableRow(
        AppStrings.saleTypeLabel,
        selectedTransactionType.value?.value ?? '',
      ),
      _buildCompactPdfTableRow(
        AppStrings.grossCommission,
        grossCommissionController.text,
      ),
      _buildCompactPdfTableRow(
        AppStrings.commissionSplit,
        '${commissionSplitController.text}%',
      ),
      _buildCompactPdfTableRow(
        AppStrings.dateReleased,
        dateReleasedController.text,
      ),
      _buildCompactPdfTableRow(
        AppStrings.listingDate,
        listingDateController.text,
      ),
      _buildCompactPdfTableRow(
        AppStrings.expirationDate,
        expirationDateController.text,
      ),
      _buildCompactPdfTableRow(
        AppStrings.closingDate,
        closingDateController.text,
      ),
      _buildCompactPdfTableRow(
        AppStrings.escrowNumber,
        escrowNumberController.text,
      ),
      _buildSinglePdfTableRow(
        AppStrings.legalDescription,
        legalDescriptionController.text,
      ),
      _buildCompactPdfTableRow(
        AppStrings.dateReceived,
        dateReceivedController.text,
      ),
      _buildSinglePdfTableRow(AppStrings.leadSource, leadSourceController.text),
      _buildSinglePdfTableRow(
        AppStrings.amountReleased,
        amountReleasedController.text,
      ),
      _buildSinglePdfTableRow(AppStrings.firstName, firstNameController.text),
      _buildSinglePdfTableRow(AppStrings.lastName, lastNameController.text),
      _buildSinglePdfTableRow(AppStrings.email, emailController.text),
      _buildSinglePdfTableRow(
        AppStrings.representedContact,
        representedContactController.text,
      ),
      _buildSinglePdfTableRow(
        AppStrings.companyPhoneNumber,
        companyPhoneNumberController.text,
      ),
      _buildSinglePdfTableRow(
        AppStrings.contactAddress,
        contactAddressController.text,
      ),
    ];
  }

  ///////
  Future<Uint8List?> _generatePdfBytes(
    ValueNotifier<bool?> isBuyer,
    SignatureController controller,
    BuildContext context,
  ) async {
    final user = context.read<UserCubit>().state.user;
    try {
      double boxWidth = 160;
      double boxHeight = 80;

      final pdf = pw.Document();

      // Get signature image from multiple sources
      pw.ImageProvider? signatureImage;

      // First priority: Check for uploaded signature file
      if (_selectedSignatureSource == SignatureSource.uploaded &&
          signatureFile.value != null) {
        try {
          if (kIsWeb && signatureFile.value!.bytes != null) {
            signatureImage = pw.MemoryImage(signatureFile.value!.bytes!);
            // compress image
            // var rawBytes = signatureFile.value!.bytes!;
            // var decoded = img.decodeImage(rawBytes);

            // if (decoded != null) {
            //   // Resize to fit signature box (e.g., 300x150 max)
            //   var resized = img.copyResize(decoded, width: 300, height: 100);
            //   var resizedBytes = img.encodePng(resized);

            //   signatureImage = pw.MemoryImage(resizedBytes);
            // }
          } else if (signatureFile.value!.path != null) {
            final file = File(signatureFile.value!.path!);
            final bytes = await file.readAsBytes();
            signatureImage = pw.MemoryImage(bytes);
          }
        } catch (e) {
          debugPrint('Error loading uploaded signature: $e');
        }
      } else if (_selectedSignatureSource == SignatureSource.drawn &&
          controller.isNotEmpty) {
        try {
          final signatureBytes = await controller.toPngBytes();
          if (signatureBytes != null) {
            signatureImage = pw.MemoryImage(signatureBytes);
          }
        } catch (e) {
          debugPrint('Error loading drawn signature: $e');
        }
      }

      // Second priority: If no uploaded signature, try drawn signature
      if (signatureImage == null && controller.isNotEmpty) {
        try {
          final signatureBytes = await controller.toPngBytes();
          if (signatureBytes != null) {
            signatureImage = pw.MemoryImage(signatureBytes);
          }
        } catch (e) {
          debugPrint('Error loading drawn signature: $e');
        }
      }
      // if (signatureFile.value?.bytes != null &&
      //     signatureFile.value!.bytes!.length > 500000) {
      //   // For large image, expand box
      //   boxWidth = 300;
      //   boxHeight = 100;
      // }

      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          margin: pw.EdgeInsets.all(32),
          build: (pw.Context context) {
            return [
              pw.Text(
                isDocAlreadySigned.value && user?.role == UserRole.brokerage
                    ? AppStrings.signedClosingDocument
                    : AppStrings.closingDocument,
                style: pw.TextStyle(
                  fontSize: 24,
                  fontWeight: pw.FontWeight.bold,
                ),
              ),
              pw.SizedBox(height: 20),
              pw.Text(
                '${AppStrings.documentRepresenting} ${selectedRepresentingType.value?.value ?? ''}',
                style: pw.TextStyle(
                  fontSize: 14,
                  fontWeight: pw.FontWeight.bold,
                ),
              ),
              pw.SizedBox(height: 20),
              pw.Table(
                border: pw.TableBorder.all(width: 0.5),
                columnWidths: {
                  0: pw.FlexColumnWidth(1),
                  1: pw.FlexColumnWidth(1.5),
                },
                children: _buildPdfPreviewItems(),
              ),
              pw.SizedBox(height: 30),

              // Keep the title + signature box together (won't split across pages)
              pw.Inseparable(
                child: pw.Container(
                  alignment: pw.Alignment.bottomRight, // bottom-right corner
                  child: pw.Column(
                    crossAxisAlignment:
                        pw.CrossAxisAlignment.end, // align contents to right
                    mainAxisSize: pw.MainAxisSize.min,
                    children: [
                      pw.Text(
                        AppStrings.brokerageSignature,
                        style: pw.TextStyle(
                          fontSize: 12,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                      pw.SizedBox(height: 8),
                      pw.Container(
                        height: boxHeight,
                        width: boxWidth,
                        decoration: pw.BoxDecoration(
                          border: pw.Border.all(
                            color: PdfColors.black,
                            width: 1,
                          ),
                          borderRadius: pw.BorderRadius.circular(8),
                        ),
                        padding: pw.EdgeInsets.all(4),
                        child: signatureImage != null
                            ? pw.Image(signatureImage, fit: pw.BoxFit.contain)
                            : pw.Center(
                                child: pw.Text(
                                  AppStrings.noSignatureProvided,
                                  textAlign: pw.TextAlign.center,
                                  style: pw.TextStyle(
                                    fontSize: 10,
                                    color: PdfColors.grey,
                                    fontStyle: pw.FontStyle.italic,
                                  ),
                                ),
                              ),
                      ),
                      pw.SizedBox(height: 4),
                      pw.Text(
                        '${AppStrings.dates} ${DateFormat('MM-dd-yyyy').format(DateTime.now())}',
                        style: pw.TextStyle(fontSize: 10),
                      ),
                    ],
                  ),
                ),
              ),
            ];
          },
        ),
      );

      return await pdf.save();
    } catch (e) {
      debugPrint('Error generating PDF bytes: $e');
      return null;
    }
  }

  Future<void> _downloadPdf(Uint8List pdfBytes, BuildContext context) async {
    try {
      if (kIsWeb) {
        try {
          // Use the printing package for secure download on web
          await Printing.sharePdf(
            bytes: pdfBytes,
            filename:
                '${AppStrings.salesReviewDocumentPrefix}${DateTime.now().millisecondsSinceEpoch}.pdf',
          );

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(AppStrings.pdfDownloadInitiated),
              backgroundColor: Colors.green,
            ),
          );
        } catch (e) {
          // Fallback to manual download with proper headers
          final base64String = base64Encode(pdfBytes);
          final dataUrl = 'data:application/pdf;base64,$base64String';

          final anchor = html.AnchorElement()
            ..href = dataUrl
            ..download =
                '${AppStrings.salesReviewDocumentPrefix}${DateTime.now().millisecondsSinceEpoch}.pdf'
            ..style.display = 'none';

          html.document.body!.append(anchor);
          anchor.click();
          html.document.body!.children.remove(anchor);

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(AppStrings.pdfDownloadedSuccessfully),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        // For mobile/desktop - save to documents
        final output = await getApplicationDocumentsDirectory();
        final file = File(
          '${output.path}/${AppStrings.salesReviewDocumentPrefix}${DateTime.now().millisecondsSinceEpoch}.pdf',
        );
        await file.writeAsBytes(pdfBytes);

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${AppStrings.pdfSavedTo} ${file.path}'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${AppStrings.errorDownloadingPdf} $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Builder _signatureWidget(SignatureController _controller) {
    return Builder(
      builder: (context) {
        final isMobile = Responsive.isMobile(context);

        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: defaultPadding * 1.5),
          child: Column(
            children: [
              Align(
                alignment: Alignment.centerLeft,
                child: RichText(
                  text: TextSpan(
                    text: AppStrings.uploadBrokerSignature,
                    style: AppFonts.regularTextStyle(
                      14,
                      color: AppTheme.primaryTextColor,
                    ),
                    children: isEditable.value
                        ? [
                            TextSpan(
                              text: ' *',
                              style: AppFonts.regularTextStyle(
                                14,
                                color: AppTheme.textFieldMandatoryColor,
                              ),
                            ),
                          ]
                        : [],
                  ),
                ),
              ),
              SizedBox(height: defaultPadding / 2),
              isMobile
                  ? Column(
                      children: [
                        _signatureDrawingPad(context, _controller),
                        SizedBox(height: defaultPadding / 2),
                        _signatureUploadedWidget(
                          context,
                          signatureFile,
                          _signatureController,
                        ),
                      ],
                    )
                  : Row(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Expanded(
                          child: _signatureDrawingPad(context, _controller),
                        ),
                        SizedBox(width: 8),
                        // OR text
                        Text(
                          AppStrings.or,
                          style: AppFonts.regularTextStyle(
                            14,
                            color: AppTheme.primaryTextColor,
                          ),
                        ),
                        SizedBox(width: 8),
                        Expanded(
                          child: _signatureUploadedWidget(
                            context,
                            signatureFile,
                            _signatureController,
                          ),
                        ),
                      ],
                    ),
            ],
          ),
        );
      },
    );
  }

  Widget _signatureDrawingPad(
    BuildContext context,
    SignatureController _controller,
  ) {
    final isTablet = Responsive.isTablet(context);
    // signatureFile.value = null; // Clear uploaded file when drawing
    return Container(
      height: 180,
      padding: const EdgeInsets.symmetric(
        horizontal: defaultPadding,
        vertical: defaultPadding / 2,
      ),
      decoration: BoxDecoration(
        border: Border.all(color: AppTheme.signatureUploadBorderColor),
        borderRadius: BorderRadius.circular(25),
      ),
      child: Column(
        children: [
          Expanded(
            child: Container(
              child: Signature(
                controller: _controller,
                backgroundColor: Colors.white,
              ),
            ),
          ),
          const SizedBox(height: 8),
          Container(
            decoration: BoxDecoration(borderRadius: BorderRadius.circular(25)),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                _actionButton(
                  isTablet: isTablet,
                  label: AppStrings.clear,
                  onPressed: () => _controller.clear(),
                ),
                const SizedBox(width: 8),
                // _actionButton(
                //   isTablet: isTablet,
                //   label: AppStrings.save,
                //   onPressed: () async {
                //     if (_controller.isNotEmpty) {
                //       final signatureBytes = await _controller.toPngBytes();
                //       if (signatureBytes != null) {
                //         // Store the drawn signature as a PlatformFile for consistency
                //         signatureFile.value = PlatformFile(
                //           name:
                //               'drawn_signature_${DateTime.now().millisecondsSinceEpoch}.png',
                //           size: signatureBytes.length,
                //           bytes: signatureBytes,
                //         );

                //         ScaffoldMessenger.of(context).showSnackBar(
                //           SnackBar(
                //             content: Text('Signature saved successfully'),
                //             backgroundColor: Colors.green,
                //           ),
                //         );
                //       }
                //     }
                //   },
                // ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _actionButton({
    required isTablet,
    required String label,
    required VoidCallback onPressed,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppTheme.signatureUploadBorderColor),
      ),
      child: TextButton(
        onPressed: onPressed,
        style: TextButton.styleFrom(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
        child: Padding(
          padding: EdgeInsets.symmetric(
            horizontal: isTablet ? defaultPadding / 2 : defaultPadding,
          ),
          child: Text(
            label,
            style: AppFonts.mediumTextStyle(
              isTablet ? 10 : 12,
              color: AppTheme.black,
            ),
          ),
        ),
      ),
    );
  }

  static PlatformFile? loadSignatureFromState() {
    final state = FormStateManager().getState();
    final base64Signature = state['selectedSignature'];
    final signatureName = state['selectedSignatureName'];

    if (base64Signature != null && base64Signature.isNotEmpty) {
      try {
        final bytes = base64Decode(base64Signature);
        return PlatformFile(
          name: signatureName ?? 'signature.png',
          size: bytes.length,
          bytes: bytes,
        );
      } catch (e) {
        debugPrint("Error decoding saved signature: $e");
      }
    }
    return null;
  }

  Widget _signatureUploadedWidget(
    BuildContext context,
    ValueNotifier<PlatformFile?> fileNotifier,
    SignatureController _controller,
  ) {
    if (signatureFile.value == null) {
      signatureFile.value = loadSignatureFromState();
    }
    final isTablet = Responsive.isTablet(context);
    return ValueListenableBuilder<PlatformFile?>(
      valueListenable: fileNotifier,
      builder: (context, file, child) {
        final isSmallMobile = Responsive.isSmallMobile(context);
        return buildDottedBorderContainerWithRadius(
          borderColor: AppTheme.signatureUploadDottedColor,
          borderRadius: 25,
          child: ClipRRect(
            borderRadius: BorderRadiusGeometry.all(Radius.circular(25)),
            child: Container(
              height: 180,
              padding: EdgeInsets.all(
                isSmallMobile ? defaultPadding / 2 : defaultPadding,
              ),
              color: AppTheme.docUploadBgColor,
              alignment: Alignment.center,
              child: file != null
                  ? Stack(
                      children: [
                        Center(
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.check_circle,
                                color: Colors.green,
                                size: isSmallMobile ? 16 : 20,
                              ),
                              SizedBox(width: isSmallMobile ? 8 : 12),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Text(
                                      file.name,
                                      style: AppFonts.mediumTextStyle(
                                        isSmallMobile ? 12 : 14,
                                        color: Colors.green.shade700,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                      textAlign: TextAlign.center,
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      '${(file.size / 1024).toStringAsFixed(1)} KB',
                                      style: AppFonts.regularTextStyle(
                                        isSmallMobile ? 10 : 12,
                                        color: Colors.green.shade600,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                    const SizedBox(height: 4),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                        Positioned(
                          top: 0,
                          right: 0,
                          child: GestureDetector(
                            onTap: () => fileNotifier.value = null,
                            child: Icon(
                              Icons.close,
                              color: Colors.red,
                              size: isSmallMobile ? 16 : 20,
                            ),
                          ),
                        ),
                      ],
                    )
                  : Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        GestureDetector(
                          onTap: () async {
                            // Clear drawn signature when uploading file
                            // _controller.clear();
                            await _pickImage(
                              context,
                              fileNotifier,
                              APIConsts.imageExtensionsAllowed,
                            );
                            _backupFormState(context);
                          },
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 5,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              border: Border.all(
                                color: AppTheme.signatureUploadBorderColor,
                              ),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Material(
                              color: Colors.transparent,
                              child: InkWell(
                                mouseCursor: SystemMouseCursors.click,
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Image.asset(
                                      '$iconAssetpath/upload.png',
                                      height: 18,
                                      width: 18,
                                    ),
                                    const SizedBox(width: 4),
                                    Text(
                                      AppStrings.upload,
                                      style: AppFonts.mediumTextStyle(
                                        14,
                                        color: AppTheme.black,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(height: 18),
                        Text(
                          AppStrings.uploadText,
                          textAlign: TextAlign.center,
                          style: AppFonts.mediumTextStyle(
                            isTablet ? 12 : 14,
                            color: AppTheme.black,
                          ),
                        ),
                        Text(
                          AppStrings.uploadFormat,
                          textAlign: TextAlign.center,
                          style: AppFonts.regularTextStyle(
                            12,
                            color: AppTheme.black,
                          ),
                        ),
                      ],
                    ),
            ),
          ),
        );
      },
    );
  }

  void _reset(BuildContext context, ValueNotifier<bool?> isBuyer) {
    isEditable.value = false;
    shouldShowEditButton.value = true;
    FormStateManager().saveState({
      'shouldShowEditButton': shouldShowEditButton.value.toString(),
    });
    _formKey.currentState?.reset();
    _backupFormState(context);
  }

  Widget _docRepresentingRadio(
    ValueNotifier<bool?> isBuyer,
    bool isMandatory,
    BuildContext context,
  ) {
    final saleDocCubit = context.read<SalesDetailsCubit>();

    return ValueListenableBuilder<RepresentingTypes?>(
      valueListenable: selectedRepresentingType, // matches type
      builder: (context, selectedItem, child) {
        return Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            RichText(
              text: TextSpan(
                text: representing,
                style: AppFonts.regularTextStyle(
                  14,
                  color: AppTheme.primaryTextColor,
                ),
                children: isMandatory && isEditable.value
                    ? [
                        TextSpan(
                          text: ' *',
                          style: AppFonts.regularTextStyle(
                            14,
                            color: AppTheme.textFieldMandatoryColor,
                          ),
                        ),
                      ]
                    : [],
              ),
            ),
            const SizedBox(width: defaultPadding),

            if (saleDocCubit.representingTypesData.isNotEmpty)
              ...saleDocCubit.representingTypesData.map((item) {
                return Padding(
                  padding: const EdgeInsets.only(right: defaultPadding),
                  child: RadioOption<String>(
                    value: item?.id ?? "",
                    groupValue: selectedItem?.id ?? "", // use selectedItem
                    onChanged: (value) {
                      if (item != null) {
                        selectedRepresentingType.value =
                            item; // update full model
                        context
                            .read<SalesDetailsCubit>()
                            .updateRepresentingType(item);
                        _backupFormState(context);
                      }
                    },
                    label: item?.value ?? "",
                    enabled: isEditable.value,
                  ),
                );
              }).toList(),
          ],
        );
      },
    );
  }

  final GlobalKey _fieldKey = GlobalKey();
  Widget buildTextField(
    TextEditingController controller,
    String label,
    String image,
    String value, {
    VoidCallback? onTap,
    bool isMandatory = false,
    String? Function(String?)? validator,
    TextInputType? keyboardType,
    List<TextInputFormatter>? inputFormatters,
    int maxLines = 1,
    BuildContext? context,
    bool enabled = true,
  }) {
    // Check if this is a date field
    final isDateField = [
      AppStrings.dateDeposited,
      AppStrings.dateReleased,
      AppStrings.listingDate,
      AppStrings.expirationDate,
      AppStrings.closingDate,
      AppStrings.dateReceived,
    ].contains(label);
    // Get the appropriate key for date fields
    final fieldKey = isDateField ? _getFieldKey(label) : null;
    final saleDocCubit = context?.read<SalesDetailsCubit>();
    final leadSourceOptions = saleDocCubit?.leadSourceData;
    return Column(
      key: fieldKey, // Assign the key here
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        RichText(
          text: TextSpan(
            text: label,
            style: AppFonts.regularTextStyle(
              14,
              color: AppTheme.primaryTextColor,
            ),
            children: isMandatory && isEditable.value && enabled
                ? [
                    TextSpan(
                      text: ' *',
                      style: AppFonts.regularTextStyle(
                        14,
                        color: AppTheme.textFieldMandatoryColor,
                      ),
                    ),
                  ]
                : [],
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          enabled: isEditable.value && enabled,
          readOnly:
              isDateField || label == leadSource, // Make date fields read-only
          key: label == leadSource ? _fieldKey : null,
          onTap: isDateField
              ? () => _openCalendarForField(context!, label)
              : label == leadSource
              ? () {
                  _backupFormState(context!);
                  _showDropdown(context!, leadSourceOptions, controller);
                }
              : onTap,
          autovalidateMode: AutovalidateMode.onUserInteraction,
          decoration: InputDecoration(
            prefixIcon: Container(
              margin: const EdgeInsets.only(left: 1, top: 1.5, bottom: 1.5),
              padding: const EdgeInsets.only(left: 2),
              decoration: BoxDecoration(
                color: enabled ? AppTheme.white : Colors.transparent,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(55),
                  bottomLeft: Radius.circular(55),
                ),
              ),
              width: 40,
              child: Center(
                child: Image.asset(
                  '$iconAssetpath/$image',
                  height: 18,
                  width: 18,
                ),
              ),
            ),
            filled: true,
            fillColor: AppTheme.formSubsectionBgColor,
            contentPadding: const EdgeInsets.symmetric(
              vertical: 10,
              horizontal: defaultPadding,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(55),
              borderSide: BorderSide(color: AppTheme.textFieldBorder),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(55),
              borderSide: const BorderSide(color: AppTheme.textFieldBorder),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(55),
              borderSide: const BorderSide(
                color: AppTheme.primaryColor,
                width: 1,
              ),
            ),
            isDense: true,
          ),
          validator: validator ?? _getValidatorForField(label),
          keyboardType: keyboardType,
          inputFormatters: inputFormatters,
          maxLines: maxLines,
          style: AppFonts.regularTextStyle(
            isMobile ? 16 : 14,
            color: AppTheme.primaryTextColor,
          ),
        ),
      ],
    );
  }

  void _showDropdown(
    BuildContext context,
    List<LeadSource?>? _options,
    TextEditingController _controller,
  ) async {
    isEditable.value = true;
    final RenderBox renderBox =
        _fieldKey.currentContext!.findRenderObject() as RenderBox;
    final Size size = renderBox.size;
    final Offset offset = renderBox.localToGlobal(Offset.zero);

    final selected = await showMenu<String>(
      context: context,
      position: RelativeRect.fromLTRB(
        offset.dx,
        offset.dy + size.height, // directly below TextField
        offset.dx + size.width,
        0,
      ),
      color: AppTheme.white,
      items: [
        PopupMenuItem<String>(
          enabled: false, // keeps dropdown open for scroll
          padding: EdgeInsets.only(
            left: defaultPadding,
          ), // remove default padding
          child: Container(
            height: 200, // dropdown height
            width: size.width,
            color: AppTheme.white,
            child: Scrollbar(
              thumbVisibility: true,
              child: ListView(
                children: _options!
                    .map(
                      (option) => ListTile(
                        title: Text(
                          option?.value ?? '',
                          style: AppFonts.regularTextStyle(
                            14,
                            color: AppTheme.primaryTextColor,
                          ), //  text color
                        ),
                        hoverColor: Colors.blueGrey.shade700, // hover effect
                        onTap: () {
                          Navigator.pop(context, option?.value);
                        },
                      ),
                    )
                    .toList(),
              ),
            ),
          ),
        ),
      ],
    );
    if (selected != null) {
      _controller.text = selected;
      isEditable.value = true;
      selectedLeadSourceType.value = _options.firstWhere(
        (item) => item?.value == selected,
      );
      context.read<SalesDetailsCubit>().updateLeadSource(
        selectedLeadSourceType.value,
      );
      _backupFormState(context);
    }
  }

  Future<void> _showSignatureSelectionDialog(
    BuildContext context,
    ValueNotifier<bool?> isBuyer,
  ) async {
    final selectedSignature = await showDialog<SignatureSource>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        // Create a local state variable for the dialog
        SignatureSource? tempSelectedSignature;

        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: Text(
                AppStrings.selectSignature,
                style: AppFonts.mediumTextStyle(18),
              ),
              content: Container(
                width: 500,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      AppStrings.pleaseSelectOneOfTheSignaturesBelow,
                      style: AppFonts.regularTextStyle(14),
                    ),
                    SizedBox(height: 20),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        // Uploaded signature preview
                        if (signatureFile.value != null)
                          Expanded(
                            child: InkWell(
                              onTap: () {
                                setState(() {
                                  tempSelectedSignature =
                                      SignatureSource.uploaded;
                                });
                              },
                              child: Container(
                                padding: EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  border: Border.all(
                                    color:
                                        tempSelectedSignature ==
                                            SignatureSource.uploaded
                                        ? AppTheme.primaryBlueColor
                                        : Colors.grey,
                                    width:
                                        tempSelectedSignature ==
                                            SignatureSource.uploaded
                                        ? 2
                                        : 1,
                                  ),
                                  borderRadius: BorderRadius.circular(8),
                                  color:
                                      tempSelectedSignature ==
                                          SignatureSource.uploaded
                                      ? AppTheme.primaryBlueColor.withOpacity(
                                          0.1,
                                        )
                                      : Colors.transparent,
                                ),
                                child: Column(
                                  children: [
                                    Container(
                                      height: 100,
                                      width: 150,
                                      decoration: BoxDecoration(
                                        border: Border.all(
                                          color: Colors.grey.shade300,
                                        ),
                                        borderRadius: BorderRadius.circular(4),
                                      ),
                                      child:
                                          kIsWeb &&
                                              signatureFile.value?.bytes != null
                                          ? ClipRRect(
                                              borderRadius:
                                                  BorderRadius.circular(4),
                                              child: Image.memory(
                                                signatureFile.value!.bytes!,
                                                fit: BoxFit.contain,
                                              ),
                                            )
                                          : signatureFile.value?.path != null
                                          ? ClipRRect(
                                              borderRadius:
                                                  BorderRadius.circular(4),
                                              child: Image.file(
                                                File(
                                                  signatureFile.value!.path!,
                                                ),
                                                fit: BoxFit.contain,
                                              ),
                                            )
                                          : Center(
                                              child: Text(
                                                'No preview available',
                                              ),
                                            ),
                                    ),
                                    SizedBox(height: 8),
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        if (tempSelectedSignature ==
                                            SignatureSource.uploaded)
                                          Icon(
                                            Icons.check_circle,
                                            color: AppTheme.primaryBlueColor,
                                            size: 16,
                                          ),
                                        if (tempSelectedSignature ==
                                            SignatureSource.uploaded)
                                          SizedBox(width: 4),
                                        Text(
                                          'Uploaded Signature',
                                          style: AppFonts.regularTextStyle(
                                            12,
                                            color:
                                                tempSelectedSignature ==
                                                    SignatureSource.uploaded
                                                ? AppTheme.primaryBlueColor
                                                : Colors.black,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),

                        if (signatureFile.value != null &&
                            _signatureController.isNotEmpty)
                          SizedBox(width: 16),

                        // Drawn signature preview
                        if (_signatureController.isNotEmpty)
                          Expanded(
                            child: InkWell(
                              onTap: () {
                                setState(() {
                                  tempSelectedSignature = SignatureSource.drawn;
                                });
                              },
                              child: Container(
                                padding: EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  border: Border.all(
                                    color:
                                        tempSelectedSignature ==
                                            SignatureSource.drawn
                                        ? AppTheme.primaryBlueColor
                                        : Colors.grey,
                                    width:
                                        tempSelectedSignature ==
                                            SignatureSource.drawn
                                        ? 2
                                        : 1,
                                  ),
                                  borderRadius: BorderRadius.circular(8),
                                  color:
                                      tempSelectedSignature ==
                                          SignatureSource.drawn
                                      ? AppTheme.primaryBlueColor.withOpacity(
                                          0.1,
                                        )
                                      : Colors.transparent,
                                ),
                                child: Column(
                                  children: [
                                    Container(
                                      height: 100,
                                      width: 150,
                                      decoration: BoxDecoration(
                                        border: Border.all(
                                          color: Colors.grey.shade300,
                                        ),
                                        borderRadius: BorderRadius.circular(4),
                                      ),
                                      child: FutureBuilder<Uint8List?>(
                                        future: _signatureController
                                            .toPngBytes(),
                                        builder: (context, snapshot) {
                                          if (snapshot.hasData &&
                                              snapshot.data != null) {
                                            return ClipRRect(
                                              borderRadius:
                                                  BorderRadius.circular(4),
                                              child: Image.memory(
                                                snapshot.data!,
                                                fit: BoxFit.contain,
                                              ),
                                            );
                                          } else {
                                            return Center(
                                              child: CircularProgressIndicator(
                                                strokeWidth: 2,
                                              ),
                                            );
                                          }
                                        },
                                      ),
                                    ),
                                    SizedBox(height: 8),
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        if (tempSelectedSignature ==
                                            SignatureSource.drawn)
                                          Icon(
                                            Icons.check_circle,
                                            color: AppTheme.primaryBlueColor,
                                            size: 16,
                                          ),
                                        if (tempSelectedSignature ==
                                            SignatureSource.drawn)
                                          SizedBox(width: 4),
                                        Text(
                                          'Drawn Signature',
                                          style: AppFonts.regularTextStyle(
                                            12,
                                            color:
                                                tempSelectedSignature ==
                                                    SignatureSource.drawn
                                                ? AppTheme.primaryBlueColor
                                                : Colors.black,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                      ],
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    if (Navigator.of(context).canPop()) {
                      Navigator.pop(context, null);
                    }
                  },
                  child: Text(
                    AppStrings.cancel,
                    style: AppFonts.mediumTextStyle(14),
                  ),
                ),
                ElevatedButton(
                  onPressed: tempSelectedSignature != null
                      ? () {
                          if (Navigator.of(context).canPop()) {
                            Navigator.pop(context, tempSelectedSignature);
                          }
                        }
                      : null, // Disable button if no selection
                  style: ElevatedButton.styleFrom(
                    backgroundColor: tempSelectedSignature != null
                        ? AppTheme.primaryBlueColor
                        : Colors.grey,
                  ),
                  child: Text(
                    AppStrings.continueText,
                    style: TextStyle(color: Colors.white),
                  ),
                ),
              ],
            );
          },
        );
      },
    );

    if (selectedSignature != null) {
      // Store the selected signature type
      _selectedSignatureSource = selectedSignature;
      // Continue with PDF preview
      _showPdfPreview(isBuyer, context, _signatureController);
    }
    // If selectedSignature is null (canceled), do nothing
  }

  Widget buildTextFieldWithButtons(
    String label,
    String image,
    String value, {
    VoidCallback? onTap,
    String fileSize = '',
    bool isMandatory = false,
  }) {
    final isDocField =
        label == AppStrings.closingDocument ||
        label == AppStrings.signedClosingDocument;
    return ValueListenableBuilder(
      valueListenable: closingDocFileName,
      builder: (context, value, child) {
        return Material(
          color: Colors.transparent,
          child: InkWell(
            mouseCursor: SystemMouseCursors.click,
            onTap: onTap,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                RichText(
                  text: TextSpan(
                    text: label,
                    style: AppFonts.regularTextStyle(
                      14,
                      color: AppTheme.primaryTextColor,
                    ),
                    children: isMandatory && isEditable.value
                        ? [
                            TextSpan(
                              text: ' *',
                              style: AppFonts.regularTextStyle(
                                14,
                                color: AppTheme.textFieldMandatoryColor,
                              ),
                            ),
                          ]
                        : [],
                  ),
                ),
                const SizedBox(height: 6),
                Container(
                  padding: EdgeInsets.symmetric(
                    vertical: isDocField && isEditable.value ? 8 : 10,
                    horizontal: 18,
                  ),
                  decoration: BoxDecoration(
                    color: isEditable.value
                        ? Colors.white
                        : AppTheme.formSubsectionBgColor,
                    borderRadius: BorderRadius.circular(25),
                    border: Border.all(
                      color: AppTheme.signatureUploadBorderColor,
                    ),
                  ),
                  child: Row(
                    children: [
                      Image.asset(
                        '$iconAssetpath/$image',
                        height: 18,
                        width: 18,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: RichText(
                          strutStyle: const StrutStyle(
                            height: 1,
                            forceStrutHeight: true,
                          ),
                          text: TextSpan(
                            children: [
                              TextSpan(
                                text: closingDocFileName.value.isNotEmpty
                                    ? closingDocFileName.value
                                    : FormStateManager()
                                              .getState()['closingDocFileName'] ??
                                          '',
                                style: AppFonts.regularTextStyle(
                                  14,
                                  color: AppTheme.primaryTextColor,
                                ),
                              ),
                              fileSize.isNotEmpty
                                  ? TextSpan(
                                      text: '\n$fileSize',
                                      style: AppFonts.regularTextStyle(
                                        10,
                                        color: Colors.grey,
                                      ),
                                    )
                                  : TextSpan(),
                            ],
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      if (isDocField) ..._buildDocActionButtons(context),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  List<Widget> _buildDocActionButtons(BuildContext context) {
    return [
      Tooltip(
        message: AppStrings.view,
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            mouseCursor: SystemMouseCursors.click,
            onTap: () {
              previewClosingDoc(context, closingDocFileUrl);
            },
            child: Image.asset(
              '$iconAssetpath/${AppStrings.previewImage}',
              height: 24,
              width: 24,
              semanticLabel: AppStrings.view,
            ),
          ),
        ),
      ),
      const SizedBox(width: 8),
      Tooltip(
        message: AppStrings.download,
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            mouseCursor: SystemMouseCursors.click,
            onTap: () {
              downloadClosingDoc(context, closingDocFileUrl);
            },
            child: Image.asset(
              '$iconAssetpath/${AppStrings.downloadImage}',
              height: 24,
              width: 24,
              semanticLabel: AppStrings.download,
            ),
          ),
        ),
      ),
    ];
  }

  Future<void> previewClosingDoc(BuildContext context, String url) async {
    try {
      final authRepository = AuthDataRepository();
      final token = authRepository.accessToken;
      final fullUrl = '${APIConfig.baseUrl}$url';
      final response = await http.get(
        Uri.parse(fullUrl),
        headers: {'Authorization': 'Bearer $token'},
      );

      if (response.statusCode == 200) {
        final fileName = closingDocFileName.value;
        await openPdf(response.bodyBytes, fileName: fileName);
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load file: ${response.statusCode}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> openPdf(
    Uint8List pdfBytes, {
    String fileName = "document.pdf",
  }) async {
    if (kIsWeb) {
      //  Web: tell browser it’s a PDF and open in a new tab
      final blob = html.Blob([pdfBytes], 'application/pdf');
      final url = html.Url.createObjectUrlFromBlob(blob);

      // open in new tab
      html.window.open(url, "_blank");

      // release the object URL when done
      // html.Url.revokeObjectUrl(url);
    } else {
      //  Mobile: save to temp dir and open with native PDF viewer
      final dir = await getTemporaryDirectory();
      final file = File('${dir.path}/$fileName');
      await file.writeAsBytes(pdfBytes, flush: true);

      await OpenFilex.open(file.path); // opens with installed PDF reader
    }
  }

  Future<void> downloadClosingDoc(BuildContext context, String url) async {
    if (url.isEmpty) return;

    try {
      final authRepository = AuthDataRepository();
      final token = authRepository.accessToken;

      final fullUrl = '${APIConfig.baseUrl}$url';

      final response = await http.get(
        Uri.parse(fullUrl),
        headers: {'Authorization': 'Bearer $token'},
      );

      if (response.statusCode != 200) {
        throw Exception('Failed to download file: ${response.statusCode}');
      }

      final bytes = response.bodyBytes;

      // Validate PDF bytes (optional but recommended)
      if (bytes.length < 4 || String.fromCharCodes(bytes.take(4)) != '%PDF') {
        throw Exception('Downloaded file is not a valid PDF');
      }

      final fileName = closingDocFileName.value;

      if (kIsWeb) {
        // Web: create a blob and open/download it
        final blob = html.Blob([bytes], 'application/pdf');
        final url = html.Url.createObjectUrlFromBlob(blob);
        final anchor = html.document.createElement('a') as html.AnchorElement;
        anchor.href = url;
        anchor.download = fileName;
        anchor.click();
        html.Url.revokeObjectUrl(url);
        debugPrint("File downloaded to: $url");
      } else {
        // Mobile: save to Downloads or Documents
        Directory? dir;
        if (Platform.isAndroid || Platform.isIOS) {
          dir = await getApplicationDocumentsDirectory();
        } else {
          dir = await getDownloadsDirectory();
        }

        final file = File("${dir!.path}/$fileName");
        await file.writeAsBytes(bytes, flush: true);

        debugPrint("File downloaded to: ${file.path}");

        await OpenFilex.open(file.path);
      }
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text("${AppStrings.downloading} '$fileName'..."),
          backgroundColor: const ui.Color.fromARGB(255, 247, 145, 2),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to download file: $e'),
          backgroundColor: Colors.red,
        ),
      );
      debugPrint('Error downloading file: $e');
    }
  }

  Future<void> _pickImage(
    BuildContext context,
    ValueNotifier<PlatformFile?> fileNotifier,
    List<String> allowedExtensions,
  ) async {
    if (kIsWeb) {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.image,
      );
      if (result != null && result.files.isNotEmpty) {
        final file = result.files.single;
        // Extract the file extension and convert to lowercase
        final fileExtension = file.extension?.toLowerCase() ?? '';
        // Check if the extension is actually allowed
        if (!allowedExtensions.contains(fileExtension)) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                '${AppStrings.invalidImageType} ${APIConsts.imageExtensionsAllowed.join(", ")}',
              ),
              backgroundColor: Colors.red,
              duration: Duration(seconds: 3),
            ),
          );
          return;
        }
        if (file.size > maxSignatureFileSize) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(AppStrings.fileTooLarge),
              backgroundColor: Colors.red,
              duration: Duration(seconds: 3),
            ),
          );
          return;
        }
        try {
          final bytes = file.bytes;
          if (bytes == null) throw Exception("No bytes found");
          await decodeImageFromList(bytes); // validate
          fileNotifier.value = file;
        } catch (_) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(AppStrings.invalidSignatureFile),
              backgroundColor: Colors.red,
              duration: Duration(seconds: 3),
            ),
          );
        }
      }
    } else if (Platform.isAndroid || Platform.isIOS) {
      final picker = ImagePicker();

      showModalBottomSheet(
        context: context,
        builder: (ctx) => SafeArea(
          child: Wrap(
            children: [
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: const Text(AppStrings.gallery),
                onTap: () async {
                  final XFile? pickedFile = await picker.pickImage(
                    source: ImageSource.gallery,
                  );
                  if (pickedFile != null) {
                    final file = File(pickedFile.path);
                    final size = await file.length();
                    if (size > maxSignatureFileSize) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text(AppStrings.fileTooLarge),
                          backgroundColor: Colors.red,
                          duration: Duration(seconds: 3),
                        ),
                      );
                    } else {
                      fileNotifier.value = PlatformFile(
                        name: pickedFile.name,
                        path: pickedFile.path,
                        size: size,
                      );
                    }
                  }
                  Navigator.pop(ctx);
                },
              ),
              ListTile(
                leading: const Icon(Icons.camera_alt),
                title: const Text(AppStrings.camera),
                onTap: () async {
                  final XFile? pickedFile = await picker.pickImage(
                    source: ImageSource.camera,
                  );
                  if (pickedFile != null) {
                    final file = File(pickedFile.path);
                    final size = await file.length();
                    if (size > maxSignatureFileSize) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text(AppStrings.fileTooLarge),
                          backgroundColor: Colors.red,
                          duration: Duration(seconds: 3),
                        ),
                      );
                    } else {
                      try {
                        final bytes = await file.readAsBytes();
                        await decodeImageFromList(bytes); // validate
                        fileNotifier.value = PlatformFile(
                          name: pickedFile.name,
                          path: pickedFile.path,
                          size: size,
                        );
                      } catch (_) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text(AppStrings.corruptedImage),
                            backgroundColor: Colors.red,
                            duration: Duration(seconds: 3),
                          ),
                        );
                      }
                    }
                  }
                  Navigator.pop(ctx);
                },
              ),
            ],
          ),
        ),
      );
    } else {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.image,
      );
      if (result != null && result.files.isNotEmpty) {
        final file = result.files.single;
        if (file.size > maxSignatureFileSize) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(AppStrings.fileTooLarge),
              backgroundColor: Colors.red,
              duration: Duration(seconds: 3),
            ),
          );
          return;
        }
        fileNotifier.value = file;
      }
    }
  }

  Future<Uint8List> _generateAndDownloadPDF(
    ValueNotifier<bool?> isBuyer,
    BuildContext context,
    SignatureController controller,
  ) async {
    try {
      final pdf = pw.Document();

      // Get signature image
      pw.ImageProvider? signatureImage;

      // Priority 1: uploaded file
      if (signatureFile.value != null) {
        try {
          if (kIsWeb && signatureFile.value!.bytes != null) {
            signatureImage = pw.MemoryImage(signatureFile.value!.bytes!);
          } else if (signatureFile.value!.path != null) {
            final file = File(signatureFile.value!.path!);
            final bytes = await file.readAsBytes();
            signatureImage = pw.MemoryImage(bytes);
          }
        } catch (e) {
          debugPrint('Error loading uploaded signature: $e');
        }
      }

      // Priority 2: drawn signature
      if (signatureImage == null && controller.isNotEmpty) {
        try {
          final signatureBytes = await controller.toPngBytes();
          if (signatureBytes != null) {
            signatureImage = pw.MemoryImage(signatureBytes);
          }
        } catch (e) {
          debugPrint('Error loading drawn signature: $e');
        }
      }

      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          margin: pw.EdgeInsets.all(32),
          build: (pw.Context context) {
            return [
              pw.Text(
                AppStrings.signedClosingDocument,
                style: pw.TextStyle(
                  fontSize: 24,
                  fontWeight: pw.FontWeight.bold,
                ),
              ),
              pw.SizedBox(height: 20),
              pw.Text(
                '${AppStrings.documentRepresenting} ${selectedRepresentingType.value?.value ?? ''}',
                style: pw.TextStyle(
                  fontSize: 14,
                  fontWeight: pw.FontWeight.bold,
                ),
              ),
              pw.SizedBox(height: 20),
              pw.Table(
                border: pw.TableBorder.all(width: 0.5),
                columnWidths: {
                  0: pw.FlexColumnWidth(1),
                  1: pw.FlexColumnWidth(1.5),
                },
                children: _buildPdfPreviewItems(),
              ),
              pw.SizedBox(height: 30),

              // Keep the title + signature box together (won't split across pages)
              pw.Inseparable(
                child: pw.Container(
                  alignment: pw.Alignment.bottomRight, // bottom-right corner
                  child: pw.Column(
                    crossAxisAlignment:
                        pw.CrossAxisAlignment.end, // align contents to right
                    mainAxisSize: pw.MainAxisSize.min,
                    children: [
                      pw.Text(
                        AppStrings.brokerageSignature,
                        style: pw.TextStyle(
                          fontSize: 12,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                      pw.SizedBox(height: 8),
                      pw.Container(
                        height: 80,
                        width: 160,
                        decoration: pw.BoxDecoration(
                          border: pw.Border.all(
                            color: PdfColors.black,
                            width: 1,
                          ),
                          borderRadius: pw.BorderRadius.circular(8),
                        ),
                        padding: pw.EdgeInsets.all(4),
                        child: signatureImage != null
                            ? pw.Image(signatureImage, fit: pw.BoxFit.contain)
                            : pw.Center(
                                child: pw.Text(
                                  AppStrings.noSignatureProvided,
                                  textAlign: pw.TextAlign.center,
                                  style: pw.TextStyle(
                                    fontSize: 10,
                                    color: PdfColors.grey,
                                    fontStyle: pw.FontStyle.italic,
                                  ),
                                ),
                              ),
                      ),
                      pw.SizedBox(height: 4),
                      pw.Text(
                        '${AppStrings.dates} ${DateFormat('MM-dd-yyyy').format(DateTime.now())}',
                        style: pw.TextStyle(fontSize: 10),
                      ),
                    ],
                  ),
                ),
              ),
            ];
          },
        ),
      );

      return await pdf.save();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${AppStrings.errorGeneratingPDF}: $e'),
          backgroundColor: Colors.red,
        ),
      );
      return Uint8List(0);
    }
  }

  // Helper method for compact 4-column table rows
  pw.TableRow _buildCompactPdfTableRow(String label1, String value1) {
    final isCurrencyField = [
      AppStrings.moneyReceived,
      AppStrings.salesVolume,
      AppStrings.grossCommission,
      AppStrings.amountReleased,
    ].contains(label1);

    return pw.TableRow(
      children: [
        pw.Padding(
          padding: pw.EdgeInsets.all(6),
          child: pw.Text(
            label1,
            style: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 10),
          ),
        ),
        pw.Padding(
          padding: pw.EdgeInsets.all(6),
          child: pw.Text(
            value1.isEmpty
                ? '-'
                : isCurrencyField
                ? formatCurrencyDollar(double.parse(value1))
                : value1,
            style: pw.TextStyle(fontSize: 10),
          ),
        ),
      ],
    );
  }

  // Helper method for single full-width table rows
  pw.TableRow _buildSinglePdfTableRow(String label, String value) {
    final isCurrencyField = [
      AppStrings.moneyReceived,
      AppStrings.salesVolume,
      AppStrings.grossCommission,
      AppStrings.commissionSplit,
      AppStrings.amountReleased,
    ].contains(label);
    return pw.TableRow(
      children: [
        pw.Padding(
          padding: pw.EdgeInsets.all(6),
          child: pw.Text(
            label,
            style: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 10),
          ),
        ),
        pw.Container(
          padding: pw.EdgeInsets.all(6),
          child: pw.Text(
            value.isEmpty
                ? '-'
                : isCurrencyField
                ? formatCurrencyDollar(double.parse(value))
                : value,
            style: pw.TextStyle(fontSize: 10),
          ),
        ),
        pw.Container(), // Empty cell
        pw.Container(), // Empty cell
      ],
    );
  }

  String? Function(String?)? _getValidatorForField(String label) {
    switch (label) {
      case AppStrings.transactionId:
        return (value) => InputValidators.validateTextLengthRange(
          value,
          fieldLabel: 'Transaction ID',
          limit: 50,
        );
      case AppStrings.transactionName:
        return (value) => InputValidators.validateTextLengthRange(
          value,
          fieldLabel: 'Transaction Name',
          limit: 100,
        );
      case AppStrings.address:
        return (value) => InputValidators.validateTextLengthRange(
          value,
          fieldLabel: 'Address',
          limit: 200,
        );
      case AppStrings.salesVolume:
        return (value) =>
            InputValidators.validateCurrency(value, allowDecimal: true);
      case AppStrings.moneyReceived:
        return (value) =>
            InputValidators.validateCurrency(value, allowDecimal: true);
      case AppStrings.grossCommission:
        return (value) =>
            InputValidators.validateCurrency(value, allowDecimal: true);

      case AppStrings.commissionSplit:
        return (value) => InputValidators.validateCommissionSplit(value);
      case AppStrings.amountReleased:
        return (value) =>
            InputValidators.validateCurrency(value, allowDecimal: true);

      case AppStrings.email:
        return (value) => InputValidators.validateEmail(value);
      case AppStrings.companyPhoneNumber:
        return (value) => InputValidators.validateCompanyPhone(value);
      case AppStrings.firstName:
        return (value) => InputValidators.validateTextLengthRange(
          value,
          fieldLabel: 'First Name',
          limit: 50,
        );
      case AppStrings.lastName:
        return (value) => InputValidators.validateTextLengthRange(
          value,
          fieldLabel: 'Last Name',
          limit: 50,
        );
      case AppStrings.escrowNumber:
        return (value) => InputValidators.validateTextLengthRange(
          value,
          fieldLabel: 'Escrow Number',
          limit: 50,
        );
      case AppStrings.legalDescription:
        return (value) => InputValidators.validateTextLengthRange(
          value,
          fieldLabel: 'Legal Description',
          limit: 500,
        );
      case AppStrings.representedContact:
        return (value) => InputValidators.validateTextLengthRange(
          value,
          fieldLabel: 'Represented Contact',
          limit: 100,
        );
      case AppStrings.contactAddress:
        return (value) => InputValidators.validateTextLengthRange(
          value,
          fieldLabel: 'Contact Address',
          limit: 200,
        );
      case AppStrings.leadSource:
        return (value) => InputValidators.validateTextLengthRange(
          value,
          fieldLabel: 'Lead Source',
          limit: 100,
        );
      // Date fields - basic validation
      case AppStrings.dateDeposited:
        return (value) {
          if (value == null || value.trim().isEmpty) {
            return '${AppStrings.pleaseEnterValidDate} $label';
          }
        };
      case AppStrings.dateReleased:
        return (value) {
          if (value == null || value.trim().isEmpty) {
            return '${AppStrings.pleaseEnterValidDate} $label';
          }
        };
      case AppStrings.listingDate:
        return (value) {
          if (value == null || value.trim().isEmpty) {
            return '${AppStrings.pleaseEnterValidDate} $label';
          }
        };
      case AppStrings.expirationDate:
        return (value) {
          if (value == null || value.trim().isEmpty) {
            return '${AppStrings.pleaseEnterValidDate} $label';
          }
        };
      case AppStrings.closingDate:
        return (value) {
          if (value == null || value.trim().isEmpty) {
            return '${AppStrings.pleaseEnterValidDate} $label';
          }
        };
      case AppStrings.dateReceived:
        return (value) {
          if (value == null || value.trim().isEmpty) {
            return '${AppStrings.pleaseEnterValidDate} $label';
          }
        };
      default:
        return null; // No validation for unknown fields
    }
  }

  Future<void> _showPdfPreview(
    ValueNotifier<bool?> isBuyer,
    BuildContext mainContext,
    SignatureController controller,
  ) async {
    final currentEditable = isEditable.value;
    try {
      // Generate PDF bytes with selected signature
      final pdfBytes = await _generatePdfBytes(
        isBuyer,
        controller,
        mainContext,
      );

      if (pdfBytes == null) {
        _restoreFormState();
        isEditable.value = currentEditable;
        ScaffoldMessenger.of(mainContext).showSnackBar(
          SnackBar(
            content: Text(AppStrings.errorGeneratingPDFPreview),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      // Show preview dialog
      final result = await showDialog(
        context: mainContext,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return Dialog(
            insetPadding: EdgeInsets.all(16),
            child: Container(
              width: MediaQuery.of(context).size.width * 0.8,
              height: MediaQuery.of(context).size.height * 0.8,
              child: Column(
                children: [
                  // Header
                  Container(
                    padding: EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryBlueColor,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(8),
                        topRight: Radius.circular(8),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          AppStrings.pdfPreview,
                          style: AppFonts.semiBoldTextStyle(
                            18,
                            color: Colors.white,
                          ),
                        ),
                        IconButton(
                          onPressed: () {
                            if (Navigator.of(context).canPop()) {
                              Navigator.pop(context);
                            }
                          },
                          icon: Icon(Icons.close, color: Colors.white),
                        ),
                      ],
                    ),
                  ),
                  // Preview content
                  Expanded(
                    child: Container(
                      padding: EdgeInsets.all(16),
                      child: kIsWeb
                          ? _buildWebPdfPreview(pdfBytes)
                          : _buildMobilePdfPreview(pdfBytes),
                    ),
                  ),
                  // Footer
                  Container(
                    padding: EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade50,
                      borderRadius: BorderRadius.only(
                        bottomLeft: Radius.circular(8),
                        bottomRight: Radius.circular(8),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        ElevatedButton(
                          onPressed: () {
                            _submitForm(
                              isBuyer,
                              context,
                              controller,
                              parentContext: mainContext,
                            );
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppTheme.primaryBlueColor,
                          ),
                          child: Text(
                            AppStrings.submit,
                            style: TextStyle(color: Colors.white),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      );
    } catch (e) {
      _restoreFormState();
      isEditable.value = currentEditable;
      ScaffoldMessenger.of(mainContext).showSnackBar(
        SnackBar(
          content: Text('${AppStrings.errorGeneratingPDFPreview} $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _submitForm(
    ValueNotifier<bool?> isBuyer,
    BuildContext context,
    SignatureController controller, {
    BuildContext? parentContext,
  }) async {
    final user = context.read<UserCubit>().state.user;
    try {
      final payload = {
        "requestingId": user?.userId,
        "formData": {
          "representingId": selectedRepresentingType.value?.id ?? '',
          "transactionFileNumber": transactionIdController.text,
          "propertyAddress": addressController.text,
          "salesVolume": salesVolumeController.text,
          "moneyReceived": moneyReceivedController.text,
          "dateDeposited": AppDateFormatter.convertDateToApiFormat(
            dateDepositedController.text,
          ).toString(),
          "transactionTypeId": selectedTransactionType.value?.id ?? '',
          "grossCommission": grossCommissionController.text,
          "commissionSplit": commissionSplitController.text,
          "dateReleased": AppDateFormatter.convertDateToApiFormat(
            dateReleasedController.text,
          ).toString(),
          "listingDate": AppDateFormatter.convertDateToApiFormat(
            listingDateController.text,
          ).toString(),
          "expirationDate": AppDateFormatter.convertDateToApiFormat(
            expirationDateController.text,
          ).toString(),
          "closingDate": AppDateFormatter.convertDateToApiFormat(
            closingDateController.text,
          ).toString(),
          "legalDescription": legalDescriptionController.text,
          "escrowNumber": escrowNumberController.text,
          "dateReceived": AppDateFormatter.convertDateToApiFormat(
            dateReceivedController.text,
          ).toString(),
          "amountReleased": amountReleasedController.text,
          "representedFirstName": firstNameController.text,
          "representedLastName": lastNameController.text,
          "representedEmail": emailController.text,
          "representedPhone": companyPhoneNumberController.text,
          "representedAddress": contactAddressController.text,
          "leadSourceId": selectedLeadSourceType.value?.id ?? '',
        },
      };
      final saleDocCubit = context.read<SalesDetailsCubit>();
      await saleDocCubit.editClosingDocument(payload, salesID);
      if (saleDocCubit.state is SalesClosingDocumentEdited) {
        if (isAgentEdit) {
          final payload = {
            'requestingId': user?.userId,
            'status': AppStrings.agentReviewed,
          };

          await saleDocCubit.updateSalesClosingDocUploadStatus(
            salesID,
            payload,
          );
          if (saleDocCubit.state is SalesClosingDocumentUploadStatusUpdated) {
            final state =
                saleDocCubit.state as SalesClosingDocumentUploadStatusUpdated;

            await AppSnackBar.showSnackBar(
              context,
              state.message,
              SnackBarType.success,
            );
            isEditable.value = false;
            isDocAlreadySigned.value = true;
            _backupFormState(context);
            _popView(context);
            _navigateToSales(context);
          } else if (saleDocCubit.state is SalesDetailsError) {
            final error = saleDocCubit.state as SalesDetailsError;

            await AppSnackBar.showSnackBar(
              context,
              error.message,
              SnackBarType.error,
            );
            _popView(context);
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(AppStrings.errorOccured),
                backgroundColor: Colors.red,
              ),
            );
          }
          // await AppSnackBar.showSnackBar(
          //   context,
          //   state.message,
          //   SnackBarType.success,
          // );
          // isEditable.value = false;
          // isDocAlreadySigned.value = true;
          // _backupFormState();
          // _popView(context);
        } else {
          final state = saleDocCubit.state as SalesClosingDocumentEdited;
          final pdfBytes = await _generateAndDownloadPDF(
            isBuyer,
            context,
            controller,
          );

          final multipartFile = MultipartFile.fromBytes(
            pdfBytes,
            filename:
                "sales_doc_${DateTime.now().millisecondsSinceEpoch}.pdf", // must end with .pdf
            contentType: MediaType('application', 'pdf'), // correct MIME type
          );

          final uploadFilePayload = {
            "id": salesID,
            "userId": user?.userId ?? '',
            "categoryType": APIConsts.salesCategoryType,
            "documentType": APIConsts.salesDocTypeApproved,
            "file": multipartFile,
            'onProgress': (sent, total) {
              debugPrint(
                "Progress: ${(sent / total * 100).toStringAsFixed(2)}%",
              );
            },
          };
          debugPrint('uploading file $uploadFilePayload');
          try {
            final saleDocCubit = context.read<SalesDetailsCubit>();
            await saleDocCubit.uploadDocFile(uploadFilePayload);
            if (saleDocCubit.state is SalesClosingDocumentFileUploaded) {
              final saleDocCubit = context.read<SalesDetailsCubit>();
              await saleDocCubit.updateSalesClosingDocUploadCommission(salesID);
              if (saleDocCubit.state
                  is SalesClosingDocumentUploadCommissionUpdated) {
                final payload = {
                  'requestingId': user?.userId,
                  'status': AppStrings.brokerageReviewed,
                };
                await saleDocCubit.updateSalesClosingDocUploadStatus(
                  salesID,
                  payload,
                );
                if (saleDocCubit.state
                    is SalesClosingDocumentUploadStatusUpdated) {
                  final state =
                      saleDocCubit.state
                          as SalesClosingDocumentUploadStatusUpdated;
                  isEditable.value = false;
                  isDocAlreadySigned.value = true;
                  _backupFormState(context);
                  _popView(context);
                  await AppSnackBar.showSnackBar(
                    context,
                    state.message,
                    SnackBarType.success,
                  );
                  _navigateToSales(parentContext);
                } else if (saleDocCubit.state is SalesDetailsError) {
                  final error = saleDocCubit.state as SalesDetailsError;
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(error.message),
                      backgroundColor: Colors.red,
                    ),
                  );
                  _popView(context);
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(AppStrings.errorOccured),
                      backgroundColor: Colors.red,
                    ),
                  );
                  _popView(context);
                }
              } else if (saleDocCubit.state is SalesDetailsError) {
                final error = saleDocCubit.state as SalesDetailsError;
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(error.message),
                    backgroundColor: Colors.red,
                  ),
                );
                _popView(context);
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(AppStrings.errorOccured),
                    backgroundColor: Colors.red,
                  ),
                );
                _popView(context);
              }
            } else if (saleDocCubit.state is SalesDetailsError) {
              final error = saleDocCubit.state as SalesDetailsError;
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(error.message),
                  backgroundColor: Colors.red,
                ),
              );
              _popView(context);
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(AppStrings.errorOccured),
                  backgroundColor: Colors.red,
                ),
              );
              _popView(context);
            }
          } catch (e) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(AppStrings.errorOccured),
                backgroundColor: Colors.red,
              ),
            );
            _popView(context);
          }
        }
      } else if (saleDocCubit.state is SalesDetailsError) {
        final error = saleDocCubit.state as SalesDetailsError;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(error.message), backgroundColor: Colors.red),
        );
        _popView(context);
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppStrings.errorOccured),
            backgroundColor: Colors.red,
          ),
        );
        _popView(context);
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${AppStrings.errorSubmittingForm} $e'),
          backgroundColor: Colors.red,
        ),
      );
      _popView(context);
    }
  }

  _popView(BuildContext context) {
    if (!isAgentEdit) {
      context.read<SalesDetailsCubit>().updateClosingDocFileName(
        closingDocFileName.value,
      );
      context.read<SalesDetailsCubit>().updateClosingDocFileUrl(
        closingDocFileUrl,
      );
      context.read<SalesDetailsCubit>().updateLeadSource(
        selectedLeadSourceType.value,
      );
      context.read<SalesDetailsCubit>().updateTransactionType(
        selectedTransactionType.value,
      );
      context.read<SalesDetailsCubit>().updateRepresentingType(
        selectedRepresentingType.value,
      );
      if (Navigator.of(context).canPop()) {
        Navigator.of(context).pop();
      }
    } else {
      // For agent edit mode, redirect back to sales tab after successful submission
      context.replace(AppRoutes.mainLayout.path);
    }
  }

  _navigateToSales(BuildContext? context) {
    if (context != null && context.mounted) {
      context.go(AppRoutes.sales.path);
    }
  }

  TextEditingController _getControllerForField(String fieldLabel) {
    switch (fieldLabel) {
      case dateDeposited:
        return dateDepositedController;
      case dateReleased:
        return dateReleasedController;
      case listingDate:
        return listingDateController;
      case expirationDateString:
        return expirationDateController;
      case closingDate:
        return closingDateController;
      case dateReceived:
        return dateReceivedController;
      default:
        throw Exception('Unknown field: $fieldLabel');
    }
  }

  void _openCalendarForField(BuildContext context, String fieldLabel) {
    currentMonth.value = DateTime.now();
    activeDateField.value = fieldLabel;

    // Store field position for calendar positioning
    final fieldKey = _getFieldKey(fieldLabel);
    if (fieldKey?.currentContext != null) {
      final RenderBox renderBox =
          fieldKey!.currentContext!.findRenderObject() as RenderBox;

      // Get position relative to the screen (considering scroll)
      final position = renderBox.localToGlobal(Offset.zero);

      // Store the position for calendar calculation
      fieldPosition.value = position;
      activeFieldKey.value = fieldKey;

      // Add a small delay to ensure the position is accurate after any layout changes
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (fieldKey.currentContext != null) {
          final updatedRenderBox =
              fieldKey.currentContext!.findRenderObject() as RenderBox;
          final updatedPosition = updatedRenderBox.localToGlobal(Offset.zero);
          fieldPosition.value = updatedPosition;
        }
      });
    }

    // Parse existing date if any
    final controller = _getControllerForField(fieldLabel);
    if (controller.text.isNotEmpty) {
      final existingDate = AppDateFormatter.parseStringToDateMMddyyyy(
        controller.text,
      );
      if (existingDate != null) {
        selectedDate.value = existingDate;
        currentMonth.value = existingDate;
      }
    } else {
      selectedDate.value = null;
      currentMonth.value = DateTime.now();
    }

    // Reset other calendar states
    selectedQuickOption.value = '';
    rangeStart.value = null;
    rangeEnd.value = null;
    isCustomRangeMode.value = false;
    customRangeStart.value = null;
    customRangeEnd.value = null;

    // Show calendar using overlay
    _showCalendarOverlay(context, fieldLabel);
  }

  GlobalKey? _getFieldKey(String fieldLabel) {
    switch (fieldLabel) {
      case dateDeposited:
        return dateDepositedKey;
      case dateReleased:
        return dateReleasedKey;
      case listingDate:
        return listingDateKey;
      case expirationDateString:
        return expirationDateKey;
      case closingDate:
        return closingDateKey;
      case dateReceived:
        return dateReceivedKey;
      default:
        return null;
    }
  }

  void _backupFormState(BuildContext context) {
    final file = signatureFile.value;
    FormStateManager().saveState({
      'isDocAlreadySigned': isDocAlreadySigned.value.toString(),
      'representingTypeString': selectedRepresentingType.value?.value ?? '',
      'isEditable': isEditable.value.toString(),
      'shouldShowEditButton': shouldShowEditButton.value.toString(),
      'transactionId': transactionIdController.text.toString(),
      'transactionName': transactionNameController.text.toString(),
      'address': addressController.text.toString(),
      'salesVolume': salesVolumeController.text.toString(),
      'moneyReceived': moneyReceivedController.text.toString(),
      'dateDeposited': dateDepositedController.text.toString(),
      'grossCommission': grossCommissionController.text.toString(),
      'commissionSplit': commissionSplitController.text.toString(),
      'dateReleased': dateReleasedController.text.toString(),
      'listingDate': listingDateController.text.toString(),
      'expirationDate': expirationDateController.text.toString(),
      'closingDate': closingDateController.text.toString(),
      'legalDescription': legalDescriptionController.text.toString(),
      'escrowNumber': escrowNumberController.text.toString(),
      'dateReceived': dateReceivedController.text.toString(),
      'amountReleased': amountReleasedController.text.toString(),
      'firstName': firstNameController.text.toString(),
      'lastName': lastNameController.text.toString(),
      'email': emailController.text.toString(),
      'companyPhoneNumber': companyPhoneNumberController.text.toString(),
      'contactAddress': contactAddressController.text.toString(),
      'leadSource': selectedLeadSourceType.value?.value ?? ''.toString(),
      'representedContact': representedContactController.text.toString(),
      'closingDocFileName': closingDocFileName.value.toString(),
      'closingDocFileUrl': closingDocFileUrl.toString(),
      'transactionTypeString':
          selectedTransactionType.value?.value ?? ''.toString(),
      'transactionTypeInt': selectedTransactionType.value?.id ?? ''.toString(),
      if (file != null && file.bytes != null) ...{
        'selectedSignature': base64Encode(file.bytes!),
        'selectedSignatureName': file.name,
      },
    });
  }

  void _restoreFormState() {
    final state = FormStateManager().getState();
    final base64Signature = state['selectedSignature'];
    final signatureName = state['selectedSignatureName'];

    if (state.isNotEmpty) {
      final savedEditable = state['isEditable'];
      if (savedEditable != null) {
        isEditable.value = savedEditable.toLowerCase() == 'true';
      }
      transactionIdController.text = state['transactionId'] ?? '';
      transactionNameController.text = state['transactionName'] ?? '';
      addressController.text = state['address'] ?? '';
      salesVolumeController.text = state['salesVolume'] ?? '';
      moneyReceivedController.text = state['moneyReceived'] ?? '';
      dateDepositedController.text = state['dateDeposited'] ?? '';
      grossCommissionController.text = state['grossCommission'] ?? '';
      commissionSplitController.text = state['commissionSplit'] ?? '';
      dateReleasedController.text = state['dateReleased'] ?? '';
      listingDateController.text = state['listingDate'] ?? '';
      expirationDateController.text = state['expirationDate'] ?? '';
      closingDateController.text = state['closingDate'] ?? '';
      legalDescriptionController.text = state['legalDescription'] ?? '';
      escrowNumberController.text = state['escrowNumber'] ?? '';
      dateReceivedController.text = state['dateReceived'] ?? '';
      amountReleasedController.text = state['amountReleased'] ?? '';
      firstNameController.text = state['firstName'] ?? '';
      lastNameController.text = state['lastName'] ?? '';
      emailController.text = state['email'] ?? '';
      companyPhoneNumberController.text = state['companyPhoneNumber'] ?? '';
      contactAddressController.text = state['contactAddress'] ?? '';
      leadSourceController.text = state['leadSource'] ?? '';
      representedContactController.text = state['representedContact'] ?? '';
      closingDocFileName.value = state['closingDocFileName'] ?? '';
      closingDocFileUrl = state['closingDocFileUrl'] ?? '';
      transactionTypeString.value = state['transactionTypeString'] ?? '';
      transactionTypeInt.value =
          int.tryParse(state['transactionTypeInt'] ?? '') ?? -1;
      if (base64Signature != null && base64Signature.isNotEmpty) {
        try {
          final bytes = base64Decode(base64Signature);
          signatureFile.value = PlatformFile(
            name: signatureName ?? 'signature.png',
            size: bytes.length,
            bytes: bytes,
          );
        } catch (e) {
          debugPrint("Error decoding signature: $e");
          signatureFile.value = null;
        }
      }
    }
  }
}
