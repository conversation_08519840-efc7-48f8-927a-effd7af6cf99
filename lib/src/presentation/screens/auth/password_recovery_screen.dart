///
/// For Forgot password / Reset password workflow
///

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import '../../../core/config/app_strings.dart' as app_strings;
import '../../../core/config/constants.dart';
import '../../../core/config/responsive.dart';
import '../../../core/navigation/web_router.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/theme/app_fonts.dart';
import '../../../core/utils/app_snack_bar.dart';
import '../../../core/utils/validators.dart';
import '../../cubit/auth/cubit/auth_cubit.dart';
import '../../shared/components/app_textfield.dart';
import '../../shared/components/elevated_button.dart';

enum PasswordRecoveryType { reset, forgot }

class PasswordRecoveryScreen extends HookWidget {
  final PasswordRecoveryType recoveryType;
  final String? token;

  PasswordRecoveryScreen({super.key, required this.recoveryType, this.token});

  @override
  Widget build(BuildContext context) {
    final formKey = useMemoized(() => GlobalKey<FormState>());
    final oldPasswordController = useTextEditingController();
    final passwordController = useTextEditingController();
    final confirmPasswordController = useTextEditingController();

    // Password visibility state
    final isOldPasswordObscured = useState(true);
    final isPasswordObscured = useState(true);
    final isConfirmPasswordObscured = useState(true);

    // Keys to force rebuild of text fields when clearing
    final oldPasswordFieldKey = useState(UniqueKey());
    final passwordFieldKey = useState(UniqueKey());
    final confirmPasswordFieldKey = useState(UniqueKey());

    final isMobile = Responsive.isMobile(context);

    useEffect(() {
      if (recoveryType == PasswordRecoveryType.forgot) {
        final authCubit = context.read<AuthCubit>();
        Future.microtask(() async {
          await authCubit.validateToken(token!);
        });
      }
      return null;
    }, []);

    return BlocConsumer<AuthCubit, AuthState>(
      listener: (context, state) {
        // TODO: implement listener
      },
      builder: (context, state) {
        if (recoveryType == PasswordRecoveryType.forgot &&
            state is TokenValidationFailure) {
          return Center(child: Text(state.error));
        } else {
          return SingleChildScrollView(
            child: Container(
              margin: const EdgeInsets.all(defaultPadding),
              width: isMobile ? double.infinity : 550,
              decoration: BoxDecoration(
                color: AppTheme.roundIconColor,
                borderRadius: BorderRadius.circular(25),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black12,
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Stack(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(25),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Header
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.symmetric(
                            vertical: defaultPadding * 2.5,
                          ),
                          decoration: BoxDecoration(
                            color: AppTheme.roundIconColor,
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(25),
                              topRight: Radius.circular(25),
                            ),
                          ),
                          child: Center(
                            child: Text(
                              recoveryType == PasswordRecoveryType.reset
                                  ? app_strings.changePasswordTitle
                                  : app_strings.enterNewLoginCredentials,
                              style: AppFonts.semiBoldTextStyle(
                                24,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),

                        // Form
                        Container(
                          decoration: BoxDecoration(
                            color: AppTheme.white,
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(25),
                              topRight: Radius.circular(25),
                            ),
                          ),
                          padding: const EdgeInsets.symmetric(
                            horizontal: defaultPadding * 2,
                            vertical: defaultPadding * 2,
                          ),
                          child: Form(
                            key: formKey,
                            child: Column(
                              children: [
                                // Text(
                                //   token ?? "no token found",
                                //   style: TextStyle(color: Colors.red),
                                // ),
                                const SizedBox(height: defaultPadding),
                                if (recoveryType ==
                                    PasswordRecoveryType.reset) ...[
                                  // Old Password
                                  Align(
                                    alignment: Alignment.centerLeft,
                                    child: Text(
                                      app_strings.oldPassword,
                                      style: AppFonts.regularTextStyle(
                                        14,
                                        color: AppTheme.primaryTextColor,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  AppTextField(
                                    key: oldPasswordFieldKey.value,
                                    controller: oldPasswordController,
                                    hintText: app_strings.enterOldPassword,
                                    isObscure: isOldPasswordObscured.value,
                                    showToggle: true,
                                    onToggleObscure: () {
                                      isOldPasswordObscured.value =
                                          !isOldPasswordObscured.value;
                                    },
                                    validator: (value) =>
                                        InputValidators.validatePassword(value),
                                    isMobile: isMobile,
                                  ),
                                  const SizedBox(height: defaultPadding),
                                ],

                                // Password
                                Align(
                                  alignment: Alignment.centerLeft,
                                  child: Text(
                                    app_strings.newPassword,
                                    style: AppFonts.regularTextStyle(
                                      14,
                                      color: AppTheme.primaryTextColor,
                                    ),
                                  ),
                                ),
                                const SizedBox(height: 8),
                                AppTextField(
                                  key: passwordFieldKey.value,
                                  controller: passwordController,
                                  hintText: app_strings.enterNewPassword,
                                  isObscure: isPasswordObscured.value,
                                  showToggle: true,
                                  onToggleObscure: () {
                                    isPasswordObscured.value =
                                        !isPasswordObscured.value;
                                  },
                                  validator: (value) =>
                                      InputValidators.validatePassword(value),
                                  isMobile: isMobile,
                                ),
                                const SizedBox(height: defaultPadding),

                                // Confirm Password
                                Align(
                                  alignment: Alignment.centerLeft,
                                  child: Text(
                                    app_strings.confirmPassword,
                                    style: AppFonts.regularTextStyle(
                                      14,
                                      color: AppTheme.primaryTextColor,
                                    ),
                                  ),
                                ),
                                const SizedBox(height: 8),
                                AppTextField(
                                  key: confirmPasswordFieldKey.value,
                                  controller: confirmPasswordController,
                                  hintText: app_strings.reEnterPassword,
                                  isObscure: isConfirmPasswordObscured.value,
                                  showToggle: true,
                                  onToggleObscure: () {
                                    isConfirmPasswordObscured.value =
                                        !isConfirmPasswordObscured.value;
                                  },
                                  validator: (value) =>
                                      InputValidators.validateConfirmPassword(
                                        value,
                                        passwordController.text,
                                      ),
                                  isMobile: isMobile,
                                ),
                                const SizedBox(height: defaultPadding * 2),

                                // Buttons
                                _buildBtnView(
                                  formKey,
                                  oldPasswordController,
                                  passwordController,
                                  confirmPasswordController,
                                  oldPasswordFieldKey,
                                  passwordFieldKey,
                                  confirmPasswordFieldKey,
                                  isOldPasswordObscured,
                                  isPasswordObscured,
                                  isConfirmPasswordObscured,
                                  context,
                                  recoveryType,
                                ),
                                const SizedBox(height: defaultPadding),
                                if (recoveryType == PasswordRecoveryType.reset)
                                  const SizedBox(height: defaultPadding),
                                _buildHomeCallbackView(context),
                                const SizedBox(height: defaultPadding),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  state is CreateNewPasswordLoading ||
                          state is ResetPasswordLoading
                      ? Positioned.fill(
                          child: Container(
                            color: Colors.transparent,
                            child: Center(child: CircularProgressIndicator()),
                          ),
                        )
                      : const SizedBox(),
                ],
              ),
            ),
          );
        }
      },
    );
  }

  Widget _buildHomeCallbackView(BuildContext context) {
    return InkWell(
      onTap: () {
        if (recoveryType == PasswordRecoveryType.forgot) {
          context.go(AppRoutes.login.path);
        } else {
          context.go(AppRoutes.dashboard.path);
        }
      },
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(
            Icons.arrow_back,
            color:
                AppTheme.secondaryTextColor, // purple arrow like in your image
            size: 20,
          ),
          const SizedBox(width: 6),
          Text(
            recoveryType == PasswordRecoveryType.forgot
                ? app_strings.backToLogin
                : app_strings.backToHome,
            style: AppFonts.regularTextStyle(
              14,
              color: AppTheme.roundIconColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBtnView(
    GlobalKey<FormState> formKey,
    TextEditingController oldPasswordController,
    TextEditingController passwordController,
    TextEditingController confirmPasswordController,
    ValueNotifier<UniqueKey> oldPasswordFieldKey,
    ValueNotifier<UniqueKey> passwordFieldKey,
    ValueNotifier<UniqueKey> confirmPasswordFieldKey,
    ValueNotifier<bool> isOldPasswordObscured,
    ValueNotifier<bool> isPasswordObscured,
    ValueNotifier<bool> isConfirmPasswordObscured,
    BuildContext context,
    PasswordRecoveryType passwordRecoveryType,
  ) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        AppButton(
          label: app_strings.clear,
          backgroundColor: AppTheme.scaffoldBgColor,
          foregroundColor: AppTheme.primaryTextColor,
          borderRadius: 25,
          padding: EdgeInsets.symmetric(
            horizontal: defaultPadding * 2,
            vertical: defaultPadding * 1.2,
          ),
          useMinSize: true,
          onPressed: () {
            oldPasswordController.clear();
            passwordController.clear();
            confirmPasswordController.clear();

            // Force text to empty string as backup
            oldPasswordController.text = '';
            passwordController.text = '';
            confirmPasswordController.text = '';

            // Reset password visibility states
            isOldPasswordObscured.value = true;
            isPasswordObscured.value = true;
            isConfirmPasswordObscured.value = true;

            // Force rebuild of text fields by changing their keys
            oldPasswordFieldKey.value = UniqueKey();
            passwordFieldKey.value = UniqueKey();
            confirmPasswordFieldKey.value = UniqueKey();

            // Reset form validation state
            WidgetsBinding.instance.addPostFrameCallback((_) {
              formKey.currentState?.reset();
            });
          },
        ),
        const SizedBox(width: defaultPadding),
        AppButton(
          label: passwordRecoveryType == PasswordRecoveryType.forgot
              ? app_strings.resetPassword
              : app_strings.changePassword,
          backgroundColor: AppTheme.roundIconColor,
          foregroundColor: Colors.white,
          borderRadius: 25,
          padding: const EdgeInsets.symmetric(
            horizontal: defaultPadding * 2,
            vertical: defaultPadding * 1.2,
          ),
          useMinSize: true,
          onPressed: () async {
            if (formKey.currentState!.validate()) {
              if (recoveryType == PasswordRecoveryType.forgot) {
                await _handleForgotPasswordAction(
                  context,
                  passwordController,
                  confirmPasswordController,
                );
              } else {
                await _handleResetPasswordAction(
                  context,
                  oldPasswordController,
                  passwordController,
                  confirmPasswordController,
                );
              }
            }
          },
        ),
      ],
    );
  }

  _handleForgotPasswordAction(
    BuildContext context,
    TextEditingController passwordController,
    TextEditingController confirmPasswordController,
  ) async {
    final authCubit = context.read<AuthCubit>();

    await authCubit.forgotPassword(
      token,
      passwordController.text.trim(),
      confirmPasswordController.text.trim(),
    );

    final state = authCubit.state;
    if (state is CreateNewPasswordSuccess) {
      await AppSnackBar.showSnackBar(
        context,
        state.message,
        SnackBarType.success,
      );

      context.go(AppRoutes.login.path);
    } else if (state is CreateNewPasswordFailure) {
      await AppSnackBar.showSnackBar(context, state.error, SnackBarType.error);
    }
  }

  _handleResetPasswordAction(
    BuildContext context,
    TextEditingController oldPasswordController,
    TextEditingController passwordController,
    TextEditingController confirmPasswordController,
  ) async {
    final authCubit = context.read<AuthCubit>();

    await authCubit.resetPassword(
      oldPasswordController.text.trim(),
      passwordController.text.trim(),
      confirmPasswordController.text.trim(),
    );

    final state = authCubit.state;
    if (state is ResetPasswordSuccess) {
      await AppSnackBar.showSnackBar(
        context,
        state.message,
        SnackBarType.success,
      );
      await authCubit.logout();

      context.go(AppRoutes.login.path);
    } else if (state is ResetPasswordFailure) {
      await AppSnackBar.showSnackBar(context, state.error, SnackBarType.error);
    }
  }
}
