import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:neorevv/src/presentation/screens/auth/components/social_login_button.dart';
import '../../../core/config/app_strings.dart' as app_strings;
import '../../../core/config/app_strings.dart';
import '../../../core/config/constants.dart';
import '../../../core/config/responsive.dart';
import '../../../core/navigation/web_router.dart';
import '../../../core/services/firebase_auth_service.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/theme/app_fonts.dart';
import '../../../core/utils/app_snack_bar.dart';
import '../../../core/utils/validators.dart';
import '../../cubit/auth/cubit/auth_cubit.dart';
import '../../cubit/user/user_cubit.dart';
import '../../shared/components/app_textfield.dart';
import '../../shared/components/elevated_button.dart';

///
/// Create password after Agent invitation/ Brokerage registration
///

class CreatePasswordScreen extends HookWidget {
  final String? inviteId;
  final String? email;
  final String? userId;

  CreatePasswordScreen({super.key, this.inviteId, this.email, this.userId});

  ValueNotifier<bool> isLoading = ValueNotifier(false);
  final emailController = TextEditingController();
  final passwordController = TextEditingController();
  final confirmPasswordController = TextEditingController();
  final FirebaseAuthService _firebaseAuthService = FirebaseAuthService();

  @override
  Widget build(BuildContext context) {
    final formKey = useMemoized(() => GlobalKey<FormState>());

    // Password visibility state
    final isPasswordObscured = useState(true);
    final isConfirmPasswordObscured = useState(true);

    // Keys to force rebuild of text fields when clearing
    final emailFieldKey = useState(UniqueKey());
    final passwordFieldKey = useState(UniqueKey());
    final confirmPasswordFieldKey = useState(UniqueKey());

    // Initialize email field if email is provided
    useEffect(() {
      if (email != null && email!.isNotEmpty) {
        emailController.text = email!;
      }
      return null;
    }, [email]);
    final isMobile = Responsive.isMobile(context);

    return SingleChildScrollView(
      child: Container(
        margin: const EdgeInsets.all(defaultPadding),
        width: isMobile ? double.infinity : 550,
        decoration: BoxDecoration(
          color: AppTheme.roundIconColor,
          borderRadius: BorderRadius.circular(25),
          boxShadow: [
            BoxShadow(
              color: Colors.black12,
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: BlocListener<AuthCubit, AuthState>(
          listener: (context, state) {
            if (state is AuthSuccess) {
              // User just logged in successfully
              context.go(AppRoutes.mainLayout.path);
            }
          },
          child: ValueListenableBuilder(
            valueListenable: isLoading,
            builder: (context, loadingState, child) {
              return Stack(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(25),

                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Header
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.symmetric(
                            vertical: defaultPadding * 2.2,
                          ),

                          child: Center(
                            child: Text(
                              app_strings.createYourLoginCredentials,
                              style: AppFonts.semiBoldTextStyle(
                                18,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),

                        // Form
                        Container(
                          decoration: BoxDecoration(
                            color: AppTheme.white,
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(25),
                              topRight: Radius.circular(25),
                            ),
                          ),
                          padding: const EdgeInsets.all(defaultPadding * 1.5),

                          child: Column(
                            children: [
                              _buildSocialButtons(context),
                              SizedBox(height: defaultPadding),

                              SizedBox(
                                height: defaultPadding * 1.5,
                                child: Text(or),
                              ),
                              Form(
                                key: formKey,
                                child: Column(
                                  children: [
                                    // Username (email)
                                    Align(
                                      alignment: Alignment.centerLeft,
                                      child: Text(
                                        app_strings.username,
                                        style: AppFonts.regularTextStyle(
                                          14,
                                          color: AppTheme.primaryTextColor,
                                        ),
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                    AppTextField(
                                      key: emailFieldKey.value,
                                      controller: emailController,
                                      hintText: app_strings.enterEmailAddress,
                                      keyboardType: TextInputType.emailAddress,
                                      validator: (value) =>
                                          InputValidators.validateEmail(value),
                                      isMobile: isMobile,
                                      enable: false,
                                    ),
                                    const SizedBox(height: defaultPadding),

                                    // Password
                                    Align(
                                      alignment: Alignment.centerLeft,
                                      child: Text(
                                        app_strings.password,
                                        style: AppFonts.regularTextStyle(
                                          14,
                                          color: AppTheme.primaryTextColor,
                                        ),
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                    AppTextField(
                                      key: passwordFieldKey.value,
                                      controller: passwordController,
                                      hintText: app_strings.enterPassword,
                                      isObscure: isPasswordObscured.value,
                                      showToggle: true,
                                      onToggleObscure: () {
                                        isPasswordObscured.value =
                                            !isPasswordObscured.value;
                                      },
                                      validator: (value) =>
                                          InputValidators.validatePassword(
                                            value,
                                          ),
                                      isMobile: isMobile,
                                    ),
                                    const SizedBox(height: defaultPadding),

                                    // Confirm Password
                                    Align(
                                      alignment: Alignment.centerLeft,
                                      child: Text(
                                        app_strings.confirmPassword,
                                        style: AppFonts.regularTextStyle(
                                          14,
                                          color: AppTheme.primaryTextColor,
                                        ),
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                    AppTextField(
                                      key: confirmPasswordFieldKey.value,
                                      controller: confirmPasswordController,
                                      hintText: app_strings.reEnterPassword,
                                      isObscure:
                                          isConfirmPasswordObscured.value,
                                      showToggle: true,
                                      onToggleObscure: () {
                                        isConfirmPasswordObscured.value =
                                            !isConfirmPasswordObscured.value;
                                      },
                                      validator: (value) =>
                                          InputValidators.validateConfirmPassword(
                                            value,
                                            passwordController.text,
                                          ),
                                      isMobile: isMobile,
                                    ),
                                    const SizedBox(height: defaultPadding * 2),

                                    // Buttons
                                    _buildBtnView(
                                      formKey,
                                      emailFieldKey,
                                      passwordFieldKey,
                                      confirmPasswordFieldKey,
                                      isPasswordObscured,
                                      isConfirmPasswordObscured,
                                      context,
                                    ),
                                    const SizedBox(height: defaultPadding),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  isLoading.value
                      ? Positioned.fill(
                          child: Container(
                            color: Colors.transparent,
                            child: Center(child: CircularProgressIndicator()),
                          ),
                        )
                      : const SizedBox(),
                ],
              );
            },
          ),
        ),
      ),
    );
  }

  /// 🔹 Social login buttons row
  Widget _buildSocialButtons(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child:
              _buildSocialButton<
                AuthGoogleSuccess,
                AuthGoogleError,
                AuthGoogleLoading
              >(
                context,
                icon: '$iconAssetpath/google.png',
                text: signInWithGmail,
                onSuccess: (ctx, state) async {
                  // await _fetchUserInfo(ctx);
                },
                onError: (ctx, state) async {
                  await AppSnackBar.showSnackBar(
                    ctx,
                    state.error,
                    SnackBarType.error,
                  );
                  ctx.go(AppRoutes.signupAgent.path);
                },
                onPressed: () => _handleGoogleSignIn(context),
              ),
        ),
        const SizedBox(width: defaultPadding),
        Expanded(
          child:
              _buildSocialButton<AuthSuccess, AuthAppleError, AuthAppleLoading>(
                context,
                icon: '$iconAssetpath/apple.png',
                text: signInWithApple,
                onSuccess: (ctx, state) async =>
                    () {}, // await _fetchUserInfo(ctx),
                onError: (ctx, state) async {
                  await AppSnackBar.showSnackBar(
                    ctx,
                    state.error,
                    SnackBarType.error,
                  );
                  ctx.go(AppRoutes.signupAgent.path);
                },
                onPressed: () => _handleAppleSignIn(context),
              ),
        ),
      ],
    );
  }

  /// 🔹 Generic Social Login Button with BlocConsumer
  Widget _buildSocialButton<
    TSuccess extends AuthState,
    TError extends AuthState,
    TLoading extends AuthState
  >(
    BuildContext context, {
    required String icon,
    required String text,
    required Future<void> Function(BuildContext, TSuccess) onSuccess,
    required Future<void> Function(BuildContext, TError) onError,
    required VoidCallback onPressed,
  }) {
    return BlocConsumer<AuthCubit, AuthState>(
      listener: (ctx, state) async {
        if (state is TSuccess) {
          await onSuccess(ctx, state);
        } else if (state is TError) {
          await onError(ctx, state);
        }
      },
      builder: (ctx, state) {
        final isLoading = state is TLoading;
        return SocialLoginButton(
          icon: Image.asset(icon, height: 20, width: 20),
          text: text,
          onPressed: isLoading ? null : onPressed,
        );
      },
    );
  }

  Widget _buildBtnView(
    GlobalKey<FormState> formKey,
    ValueNotifier<UniqueKey> emailFieldKey,
    ValueNotifier<UniqueKey> passwordFieldKey,
    ValueNotifier<UniqueKey> confirmPasswordFieldKey,
    ValueNotifier<bool> isPasswordObscured,
    ValueNotifier<bool> isConfirmPasswordObscured,
    BuildContext context,
  ) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        AppButton(
          label: app_strings.clear,
          backgroundColor: AppTheme.scaffoldBgColor,
          foregroundColor: AppTheme.primaryTextColor,
          borderRadius: 25,
          padding: EdgeInsets.symmetric(
            horizontal: defaultPadding * 2,
            vertical: defaultPadding * 1.2,
          ),
          useMinSize: true,
          onPressed: () {
            // Clear all controllers
            passwordController.clear();
            confirmPasswordController.clear();

            // Force text to empty string as backup
            passwordController.text = '';
            confirmPasswordController.text = '';

            // Reset password visibility states
            isPasswordObscured.value = true;
            isConfirmPasswordObscured.value = true;

            // Force rebuild of text fields by changing their keys
            passwordFieldKey.value = UniqueKey();
            confirmPasswordFieldKey.value = UniqueKey();

            // Reset form validation state
            WidgetsBinding.instance.addPostFrameCallback((_) {
              formKey.currentState?.reset();
            });
          },
        ),
        const SizedBox(width: defaultPadding),
        AppButton(
          label: app_strings.proceed,
          backgroundColor: AppTheme.roundIconColor,
          foregroundColor: Colors.white,
          borderRadius: 25,
          padding: const EdgeInsets.symmetric(
            horizontal: defaultPadding * 2,
            vertical: defaultPadding * 1.2,
          ),
          useMinSize: true,
          onPressed: () async {
            if (formKey.currentState!.validate()) {
              isLoading.value = true;
              ScaffoldMessenger.of(
                context,
              ).showSnackBar(SnackBar(content: Text(app_strings.proceeding)));

              await _handlePasswordReset(
                context,
                emailController,
                passwordController,
                confirmPasswordController,
              );
            }
          },
        ),
      ],
    );
  }

  Future<void> _handleGoogleSignIn(BuildContext context) async {
    final authCubit = context.read<AuthCubit>();

    // Ensure clean state before sign-in attempt
    await _firebaseAuthService.signOut();

    final signinResult = await _firebaseAuthService.signInWithGoogle();

    await authCubit.signInWithGoogle(signinResult.idToken);
    final state = authCubit.state;
    if (state is AuthSuccess) {
      await _fetchUserInfo(context);
    } else if (state is AuthGoogleError) {
      await AppSnackBar.showSnackBar(context, state.error, SnackBarType.error);
    }
  }

  Future<void> _handleAppleSignIn(BuildContext context) async {
    final authCubit = context.read<AuthCubit>();

    // Ensure clean state before sign-in attempt
    await _firebaseAuthService.signOut();

    // Firebase service will handle the warning dialog internally
    final signinResult = await _firebaseAuthService.signInWithApple(
      context: context,
    );

    // Check if Firebase Auth was successful before proceeding
    if (!signinResult.success) {
      // Show error immediately if Firebase Auth failed
      if (context.mounted && signinResult.showWarning) {
        await AppSnackBar.showSnackBar(
          context,
          signinResult.error ?? 'Apple sign-in failed',
          SnackBarType.error,
        );
      } else {
        debugPrint(
          'Login Screen: Context not mounted, cannot show Apple snackbar',
        );
      }
      return;
    }

    await authCubit.signInWithApple(signinResult.idToken);
    final state = authCubit.state;

    if (state is AuthSuccess) {
      await _fetchUserInfo(context);
    } else if (state is AuthAppleError) {
      await AppSnackBar.showSnackBar(context, state.error, SnackBarType.error);
    }
  }

  _fetchUserInfo(BuildContext context) async {
    final userCubit = context.read<UserCubit>();

    await userCubit.getUserProfile();
    final state = userCubit.state;
    if (state is UserLoaded) {
      // Navigation will be handled by AuthWrapper
      debugPrint('User loaded successfully');
    } else if (state is UserError) {
      debugPrint('Error loading user: ${state.message}');
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('${state.message} while fetching user info')),
        );
      }
    }
  }

  _handlePasswordReset(
    BuildContext context,
    TextEditingController emailController,
    TextEditingController passwordController,
    TextEditingController confirmPasswordController,
  ) async {
    final authCubit = context.read<AuthCubit>();
    final userIdToPass = userId ?? '';
    await authCubit.createPassword(
      userIdToPass,
      passwordController.text.trim(),
      confirmPasswordController.text.trim(),
    );

    final state = authCubit.state;
    if (state is CreatePasswordSuccess) {
      final snackBar = SnackBar(
        content: Text(app_strings.passwordResetSuccess),
        duration: Duration(seconds: 2),
        backgroundColor: Colors.green,
      );

      // Show snackbar and wait until it's closed
      await ScaffoldMessenger.of(context).showSnackBar(snackBar).closed;
      isLoading.value = false;
      context.go(AppRoutes.login.path);
    } else if (state is CreatePasswordFailure) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text(state.error)));
      isLoading.value = false;
    }
  }
}
