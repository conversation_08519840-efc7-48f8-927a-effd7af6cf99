// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:flutter_hooks/flutter_hooks.dart';
// import 'package:go_router/go_router.dart';
// import 'package:neorevv/src/data/repository/auth_data_repository.dart';
// import '/main_layout_screen.dart';
// import '../../../core/navigation/web_router.dart';

// import '../../cubit/auth/cubit/auth_cubit.dart';
// import 'login_screen.dart';

// /// AuthWrapper handles the authentication state and shows appropriate screen
// class AuthWrapper extends HookWidget {
//   const AuthWrapper({super.key});

//   @override
//   Widget build(BuildContext context) {
//     final isLoggedIn = useState<bool?>(
//       null,
//     ); // null = loading, true/false = determined

//     // Check authentication status on widget initialization
//     useEffect(() {
//       Future<void> checkAuthStatus() async {
//         try {
//           // Check if tokens are available

//           final authRepository = AuthDataRepository();
//           final accessToken = authRepository.accessToken;
//           if (accessToken != null) {
//             isLoggedIn.value = true;
//           } else {
//             isLoggedIn.value = false;
//           }
//           final loggedIn = accessToken != null;
//           isLoggedIn.value = loggedIn;

//           // If logged in and currently on login route, redirect to main layout
//           if (loggedIn &&
//               GoRouterState.of(context).matchedLocation ==
//                   AppRoutes.login.path) {
//             WidgetsBinding.instance.addPostFrameCallback((_) {
//               if (context.mounted) {
//                 context.go(AppRoutes.login.path);
//               }
//             });
//           }
//         } catch (e) {
//           // If there's an error checking auth status, assume not logged in
//           isLoggedIn.value = false;
//           context.go(AppRoutes.login.path);
//         }
//       }

//       checkAuthStatus();
//       return null;
//     }, []);

//     // Listen to auth state changes for login/logout
//     return BlocListener<AuthCubit, AuthState>(
//       listener: (context, state) {
//         if (state is AuthSuccess) {
//           // User just logged in successfully
//           isLoggedIn.value = true;
//           context.go(AppRoutes.mainLayout.path);
//         }
//       },
//       child: Builder(
//         builder: (context) {
//           // Show loading while checking authentication
//           if (isLoggedIn.value == null) {
//             return const Scaffold(
//               body: Center(child: CircularProgressIndicator()),
//             );
//           }

//           // Show appropriate screen based on authentication status
//           return isLoggedIn.value! ? MainLayoutScreen() : const LoginScreen();
//         },
//       ),
//     );
//   }
// }

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:neorevv/src/core/navigation/web_router.dart';
import '/main_layout_screen.dart';
import '../../cubit/auth/cubit/auth_cubit.dart';
import 'login_screen.dart';

/// AuthWrapper handles the authentication state and shows appropriate screen
class AuthWrapper extends HookWidget {
  const AuthWrapper({super.key});

  @override
  Widget build(BuildContext context) {
    final isLoggedIn = useState<bool?>(false); // default: not logged in

    // Listen to auth state changes for login/logout
    return BlocListener<AuthCubit, AuthState>(
      listener: (context, state) {
        if (state is AuthSuccess) {
          // User just logged in successfully
          context.go(AppRoutes.mainLayout.path);
          isLoggedIn.value = true;
        }
        if (state is AuthLogout) {
          // User just logged out
          isLoggedIn.value = false;
        }
      },
      child: Builder(
        builder: (context) {
          // Show appropriate screen based on authentication status
          return isLoggedIn.value! ? MainLayoutScreen() : const LoginScreen();
        },
      ),
    );
  }
}
