import 'package:flutter/material.dart';
import '../../../../core/config/responsive.dart';
import '../../../../core/theme/app_fonts.dart';
import '../../../../core/theme/app_theme.dart';

class SocialLoginButton extends StatelessWidget {
  final Widget icon;
  final String text;
  final VoidCallback? onPressed;

  const SocialLoginButton({
    super.key,
    required this.icon,
    required this.text,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: Responsive.isMobile(context) ? 50 : 45,
      child: OutlinedButton(
        onPressed: onPressed,
        style: OutlinedButton.styleFrom(
          backgroundColor: AppTheme.greyRoundBg,
          side: BorderSide.none,
          padding: EdgeInsets.symmetric(
            horizontal: Responsive.isMobile(context) ? 16 : 12,
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            icon,
            SizedBox(width: Responsive.isMobile(context) ? 12 : 8),
            Flexible(
              child: Text(
                text,
                style: AppFonts.mediumTextStyle(
                  15,
                  color: AppTheme.primaryTextColor,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
