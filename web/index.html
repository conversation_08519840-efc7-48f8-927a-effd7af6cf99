<!DOCTYPE html>
<html>
<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="/" />

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="A new Flutter project.">

  <!-- iOS meta tags & icons -->
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="neorevv">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>

  <title>neorevv</title>
  <link rel="manifest" href="manifest.json">

  <!-- Firebase SDK -->
  <script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-app-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-auth-compat.js"></script>

  <!-- Google Sign-In -->
  <script src="https://accounts.google.com/gsi/client" async defer></script>
</head>
<body>
  <!-- Firebase Configuration - Replace with your actual config -->
  <script>
    // Your web app's Firebase configuration
    // You need to replace this with your actual Firebase config
    const firebaseConfig = {
      apiKey: "AIzaSyDaIyTCa-6a0TOBQE1dnVW-wHwvUFLstF8",
      authDomain: "neorevv-86a08.firebaseapp.com",
      projectId: "neorevv-86a08",
      storageBucket: "neorevv-86a08.firebasestorage.app",
      messagingSenderId: "************",
      appId: "1:************:web:ef76c35754c3bff807b1c7"
    };

    // Initialize Firebase
    firebase.initializeApp(firebaseConfig);
  </script>

  <script src="flutter_bootstrap.js" async></script>
</body>
</html>
